package com.decathlon.sino.data.dao;

import java.util.List;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.transaction.annotation.Transactional;

import com.decathlon.sino.data.entity.RcListImportEntity;



public interface RcListImportEntityRepository extends JpaRepository<RcListImportEntity, Long>, QuerydslPredicateExecutor<RcListImportEntity> {
	@Query(value = "select * from rc_list_import where type =:bizType", nativeQuery = true)
	List<RcListImportEntity>  findByType(String bizType);
	
	@Query(value = "select * from rc_list_import where type =:bizType and person_id =:personId", nativeQuery = true)
	RcListImportEntity  findByTypeAndPersonId(String bizType, Long personId);

	@Modifying
	@Transactional
	@Query(value = "delete from rc_list_import where type =:bizType and person_id =:personId", nativeQuery = true)
	void deleteByTypeAndPersonId(String bizType, Long personId);

}
