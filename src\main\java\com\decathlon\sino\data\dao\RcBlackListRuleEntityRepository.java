package com.decathlon.sino.data.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.decathlon.sino.data.dao.basic.QueryDslBaseDao;
import com.decathlon.sino.data.entity.RcBlackListRuleEntity;

public interface RcBlackListRuleEntityRepository extends QueryDslBaseDao<RcBlackListRuleEntity> {

	@Query(value = "select * from rc_black_list_rule where status ='true'", nativeQuery = true)
	List<RcBlackListRuleEntity> findEnabled();
	
	List<RcBlackListRuleEntity> findByObjectTypeAndBizType(String objectType, String bizType);

}
