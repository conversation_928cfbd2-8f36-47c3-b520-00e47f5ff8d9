package com.decathlon.sino.common.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web 配置
 */
@Configuration
@RequiredArgsConstructor
public class WebConfig implements WebMvcConfigurer {

    private static final String PATH_PATTERNS = "/**";
    private static final String EXCLUDE_PATH_PATTERNS = "/**swagger-ui/**,/**swagger/**,/v3/api-docs/**,/doc.html/**,/webjars/**";

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new TraceIdInterceptor()).addPathPatterns(PATH_PATTERNS.split(","))
                .excludePathPatterns(EXCLUDE_PATH_PATTERNS.split(","));
    }

}
