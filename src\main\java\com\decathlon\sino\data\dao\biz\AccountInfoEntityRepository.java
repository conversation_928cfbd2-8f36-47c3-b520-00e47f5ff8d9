package com.decathlon.sino.data.dao.biz;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;

import com.decathlon.sino.data.entity.biz.AccountInfoEntity;

public interface AccountInfoEntityRepository extends JpaRepository<AccountInfoEntity, Long>, QuerydslPredicateExecutor<AccountInfoEntity> {

    @Query(value = "select * from biz_account_info where person_id =:personId ", nativeQuery = true)
    AccountInfoEntity findByPersonId(Long personId);

	AccountInfoEntity findByCardNumber(String cardNumber);
}
