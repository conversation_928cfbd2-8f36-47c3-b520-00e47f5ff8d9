spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:myDb;DB_CLOSE_DELAY=-1
    username: ${CONSENTS_DB_USERNAME:postgres}
    password: ${CONSENTS_DB_PASSWORD:mysecretpassword}
    hikar:
      maximum-pool-size: 32
      minimum-idle: 16
      data-source-properties: stringtype=unspecified
    tomcat:
      connection-properties: stringtype=unspecified

  jpa:
    hibernate:
      ddl-auto: update
      jdbc:
        batch_size: 500
        batch_versioned_data: true
    show-sql: true
    generate-ddl: false
    database-platform: org.hibernate.dialect.H2Dialect

  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6370}
    password: ${REDIS_PASSWORD:}
    database: ${REDIS_DB:0}
    timeout: ${REDIS_TIMEOUT:1800}
    lettuce:
      pool:
        max-active: 20
        max-wait: -1
        #最大阻塞等待时间(负数表示没限制)
        max-idle: 5
        min-idle: 0
