package com.decathlon.sino.kafka.biz.helper;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.decathlon.sino.data.entity.biz.AccountPointEntity;
import com.decathlon.sino.model.bo.BizPointDto;
import com.decathlon.sino.service.biz.PointDbService;
import com.decathlon.sino.service.impl.PointAuditServiceImpl;

import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
@AllArgsConstructor
public class PointListenerProcessHelper {
	
	private final PointAuditServiceImpl pointAuditServiceImpl;
	private final PointDbService pointDbService;
	
	@Async
	public void process(ConsumerRecord<String, Object> records) {
		try {
			log.info("PointListenerProcessHelper Processing message: {}", records.value());
			AccountPointEntity accountPointEntity = transform((String)records.value());
			pointDbService.save(accountPointEntity);
			
			//触发审计
			//pointAuditServiceImpl.auditRisk(null, null, null, null, null, null);
			
		} catch (Exception e) {
			log.error("Error processing message: {}", e.getMessage(), e);
			throw e;
		}		
	}

	private AccountPointEntity transform(String value) {
		BizPointDto bizPointDto = JSONUtil.toBean(value, BizPointDto.class);
		AccountPointEntity accountPointEntity = new AccountPointEntity();
		accountPointEntity.setCardNo(bizPointDto.getPointCardMap().get(0).getCardNumber());
		accountPointEntity.setPointChange(bizPointDto.getPointCardMap().get(0).getPointChange());
		accountPointEntity.setPointBalance(bizPointDto.getPointCardMap().get(0).getPointBalance());
		accountPointEntity.setEventId(bizPointDto.getEventId());
		accountPointEntity.setExternalId(bizPointDto.getExternalId());
		accountPointEntity.setPointType(bizPointDto.getEventType());
		accountPointEntity.setCreatedAt(LocalDateTime.now());
		return accountPointEntity;
	}
	
	/**
	 * 获取审计消息
	 * @param cardNumber
	 * @return Map<String, Object>
	 */
	public Map<String, Object> getAuditMessageMap(String cardNumber) {
		HashMap<String, Object> auditMessage = new HashMap<>();
		AccountPointEntity accountPointEntityMin =  pointDbService.getMinByCardNumber(cardNumber);
		auditMessage.put("minPointBalance", accountPointEntityMin.getPointBalance());
		AccountPointEntity accountPointEntityLast =  pointDbService.getLastByCardNumber(cardNumber);
		auditMessage.put("lastPointBalance", accountPointEntityLast.getPointBalance());
		return auditMessage;
	}
}
