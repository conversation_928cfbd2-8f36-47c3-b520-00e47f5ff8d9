package com.decathlon.sino.kafka.biz.helper;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.decathlon.sino.component.RiskEngineComponent;
import com.decathlon.sino.data.dao.RcBlackListRuleEntityRepository;
import com.decathlon.sino.data.entity.RcBlackListRuleEntity;
import com.decathlon.sino.data.entity.biz.AccountPointEntity;
import com.decathlon.sino.model.bo.BizPointDto;
import com.decathlon.sino.service.biz.PointDbService;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
@AllArgsConstructor
public class PointListenerProcessHelper {
	
	private final PointDbService pointDbService;
	private final MemberListenerProcessHelper memberListenerProcessHelper;
	private final PoslogListenerProcessHelper poslogListenerProcessHelper;
	private final RiskEngineComponent riskEngineComponent;
	private final RcBlackListRuleEntityRepository rcBlackListRuleEntityRepository;
	
	@Async
	public void process(ConsumerRecord<String, Object> records) {
		try {
			log.info("PointListenerProcessHelper Processing message: {}", records.value());
			AccountPointEntity accountPointEntity = transform((String)records.value());
			pointDbService.save(accountPointEntity);
			Map<String, Object> context = pointChangeAuditContextHandler(accountPointEntity);
			// Trigger risk engine evaluation for point changes
			List<RcBlackListRuleEntity> rules = rcBlackListRuleEntityRepository.findByBizType("POINT");
			for (RcBlackListRuleEntity rcBlackListRuleEntity : rules) {
				riskEngineComponent.evaluate(rcBlackListRuleEntity.getBizType(), accountPointEntity.getCardNo(), rcBlackListRuleEntity.getObjectType(), context, "SYSTEM_AUTO", true);
			}
			
		} catch (Exception e) {
			log.error("Error processing message: {}", e.getMessage(), e);
			throw e;
		}		
	}

	/**
	 * Constructs the audit context for point changes by combining member, point, and purchase audit messages.
	 */
	private Map<String, Object> pointChangeAuditContextHandler(AccountPointEntity accountPointEntity) {
		// get member audit message
		Map<String, Object> memberMessage = memberListenerProcessHelper.getAuditMessageMap(accountPointEntity.getCardNo());
		// get point audit message
		Map<String, Object> pointMessage  = this.getAuditMessageMap(accountPointEntity.getCardNo());
		// get purchase audit message
		Map<String, Object> purchaseMessage = poslogListenerProcessHelper.getPurchaseAuditMessage(accountPointEntity.getCardNo());
		Map<String, Object> context = new HashMap<>();
		context.putAll(memberMessage);
		context.putAll(pointMessage);
		context.putAll(purchaseMessage);
		return context;
	}

	/**
	 * Transform the message value to AccountPointEntity
	 * @param value
	 * @return AccountPointEntity
	 */
	private AccountPointEntity transform(String value) {
		BizPointDto bizPointDto = JSONUtil.toBean(value, BizPointDto.class);
		AccountPointEntity accountPointEntity = new AccountPointEntity();
		accountPointEntity.setCardNo(bizPointDto.getPointCardMap().get(0).getCardNumber());
		accountPointEntity.setPointChange(bizPointDto.getPointCardMap().get(0).getPointChange());
		accountPointEntity.setPointBalance(bizPointDto.getPointCardMap().get(0).getPointBalance());
		accountPointEntity.setEventId(bizPointDto.getEventId());
		accountPointEntity.setExternalId(bizPointDto.getExternalId());
		accountPointEntity.setPointType(bizPointDto.getEventType());
		accountPointEntity.setCreatedAt(LocalDateTime.now());
		return accountPointEntity;
	}
	
	/**
	 * 获取审计消息
	 * @param cardNumber
	 * @return Map<String, Object>
	 */
	public Map<String, Object> getAuditMessageMap(String cardNumber) {
		HashMap<String, Object> auditMessage = new HashMap<>();
		AccountPointEntity accountPointEntityMin =  pointDbService.getMinByCardNumber(cardNumber);
		auditMessage.put("minPointBalance", accountPointEntityMin.getPointBalance());
		AccountPointEntity accountPointEntityLast =  pointDbService.getLastByCardNumber(cardNumber);
		auditMessage.put("lastPointBalance", accountPointEntityLast.getPointBalance());
		return auditMessage;
	}
}
