package com.decathlon.sino.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.decathlon.sino.component.RiskEngineComponent;
import com.decathlon.sino.data.dao.RcBlacklistGlobalEntityRepository;
import com.decathlon.sino.data.dao.RcBlackListEntityRepository;
import com.decathlon.sino.data.entity.RcBlackListEntity;
import com.decathlon.sino.model.input.RiskInfoDto;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@AllArgsConstructor
@Slf4j
public class AduitProcessService {
	
	private final RiskEngineComponent riskEngineComponent;
	private final RcBlackListEntityRepository rcBlackListEntityRepository;
	private final RcBlacklistGlobalEntityRepository rcGlobalBlackListEntityRepository;

	/**
	 * Check risk by objectId, objectType and bizType
	 * @param bizType
	 * @param obejctId
	 * @param objectType
	 * @return RiskInfoDto
	 */
	public RiskInfoDto checkRisk(String bizType, String obejctId, String objectType) {
		
		RcBlackListEntity rcBlackListEntity =  rcBlackListEntityRepository.findByObjectIdAndObjectTypeAndBizType(bizType, obejctId, objectType);
		return getRiskInfo(rcBlackListEntity);
	}

	public RiskInfoDto auditRisk(String bizType,String objectId, String objectType, Map<String,Object> ctx,String operator,Boolean isAudit) {
		return riskEngineComponent.evaluate(bizType,objectId, objectType, ctx, operator,isAudit);
	}
	

	/**
	 * transform the blacklist entity to riskInfoDto
	 * @param rcBlackListEntity
	 * @return
	 */
	private RiskInfoDto getRiskInfo(RcBlackListEntity rcBlackListEntity) {
		RiskInfoDto riskInfoDto = new RiskInfoDto();
		log.info("getRiskInfo rcBlackListEntity: {}", rcBlackListEntity);
		return riskInfoDto;
	}
	

}
