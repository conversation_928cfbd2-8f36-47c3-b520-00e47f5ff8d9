package com.decathlon.sino.model.bo;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class GlobalBlacklist {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 黑名单类型
     * 1: IP
     * 2: 用户ID
     * 3: 设备ID
     */
    private Integer blacklistType;

    /**
     * 黑名单值
     */
    private String blacklistValue;

    /**
     * 加入黑名单原因
     */
    private String reason;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 状态
     * 0: 禁用
     * 1: 启用
     */
    private Integer status;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;
} 