package com.decathlon.sino.common.aop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

public class SensitiveJsonUtil {

    private static ObjectMapper objectMapper = new ObjectMapper();

    static {
        // 反序列化时忽略不存在的字段
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 注册处理敏感字段的扩展模块
        objectMapper.registerModule(new SensitiveFieldProcessModule());

        // 通过mixIn功能来按字段名忽略一些字段
        objectMapper.addMixIn(Object.class, IgnoreSensitiveFieldsMixin.class);
    }

    @JsonIgnoreProperties(
            value = {
                    "password",
                    "secret",
                    "token"
            }
    )
    static class IgnoreSensitiveFieldsMixin {
    }

    public static String toJsonString(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(String.format("toJsonString error, %s", e.getMessage()), e);
        }
    }

}