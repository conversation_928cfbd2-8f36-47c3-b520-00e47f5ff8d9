package com.decathlon.sino.data.entity;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.decathlon.sino.data.entity.basic.IdEntity;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@SuperBuilder
@Table(name = "rc_rule_group")
public class RcRuleGroupEntity extends IdEntity {

	private static final long serialVersionUID = 1L;

	private String groupCode;
	private String groupName;
	private Long ruleId;
	private String description;
	private Date createTime;
	private Date updateTime;

}
