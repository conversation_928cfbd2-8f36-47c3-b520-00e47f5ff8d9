package com.decathlon.sino.common.constant;

/**
 * <AUTHOR>
 * @create 2018-06-22
 */
public interface PosdataDifinition {

  String TRANSACTION_TYPECODE_RETURN = "ReturnTransaction";
  String TRANSACTION_TYPECODE_PURCHASE = "SaleTransaction";
  String TRANSACTION_TYPECODE_EXCHANGE = "ExchangeTransaction";
  String TRANSACTION_STATUS_FINISHED = "Finished";
  String TRANSACTION_STATUS_SHIPPED = "Shipped";
  String TRANSACTION_STATUS_CANCELED = "Canceled";
  String TRANSACTION_STATUS_AUTHORIZED = "Authorized";
  String UNITID_TYPECODE_RETAILSTORE = "RetailStore";
  String UNITID_TYPECODE_WEBSITE = "WebSite";
  String TOTALTYPE_TRANSACTIONGRANDAMOUNT = "TransactionGrandAmount";
  String TOTALTYPE_TRANSACTIONNETAMOUNT = "TransactionNetAmount";
  String TOTALTYPE_SALE_RETURN = "Sale";
  String TOTALTYPE_RETURN = "Return";
  String DATE_TYPE_DKT_PAYMENT = "DKT:Payment";
  String DATE_TYPE_VOUCHER = "DKT:ECheque";
  String ORIGINAL_TRANSACTIONID = "OriginalTransactionID";
  String SEQUENCENUMBER = "SequenceNumber";

  String CHANNEL_OFFLINE = "OffLine";
  String CHANNEL_ONLINE = "OnLine";
  String CHANNEL_DIGITAL = "Digital";
  String CHANNEL_PHYSICAL= "Physical";

  String POSLOG_DATETIME_TRANSACTION = "Transaction";
  String POSLOG_DATETIME_MESSAGE = "Message";
  String POSLOG_ONLINE_DATA = "TypeCode=\"DKT:MarketPlace\"";
  String REFUND = "Refund";
  String CURRENCY = "CNY";
  String DATA_ORIGIN = "MEMBERSHIP";
  String RFID = "RFID";

  String D_CN_STORE_NUMBER = "50-46-46";
  String TMALL_FLAGSHIP_STORE_NUMBER = "50-188-188";
  String TMALL_KIDS_STORE_NUMBER = "50-238-238";
  String JD_STORE_NUMBER = "50-244-244";
  String DOUYIN_STORE_NUMBER = "50-269-269";

  String DEFAULT_RETURN_PAY_METHOD = "DKT:Others";
}
