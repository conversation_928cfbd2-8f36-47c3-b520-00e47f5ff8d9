package com.decathlon.sino.data.entity;

import java.time.LocalDateTime;
import javax.persistence.Entity;
import javax.persistence.Table;
import com.decathlon.sino.data.entity.basic.IdEntity;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@SuperBuilder
@Table(name = "rc_black_list_global")
public class RcBlacklistGlobalEntity  extends IdEntity {

	private static final long serialVersionUID = 1L;

	/**
     * 黑名单类型
     * 1: IP
     * 2: 用户ID
     * 3: 设备ID
     */
    private Integer blacklistType;

    /**
     * 黑名单值
     */
    private String blacklistValue;
    
    /**
     * 加入黑名单原因
     */
    private String reason;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 状态
     * 0: 全局黑名单
     * 1: 全局白名单
     */
    private Integer status;

}
