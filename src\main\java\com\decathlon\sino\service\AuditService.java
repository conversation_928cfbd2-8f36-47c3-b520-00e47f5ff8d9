package com.decathlon.sino.service;


import com.decathlon.sino.model.input.RiskInfoDto;

import cn.hutool.json.JSONObject;

public interface AuditService {
	
	/**
	 * 1. check the risk
	 * 2. if is audit, return the audit result
	 * 3. if not audit, query the risk result from rc_black_list_audit table
	 * @param bizType
	 * @param obejctId
	 * @param objectType
	 * @param eventData
	 * @param operator
	 * @return
	 */
	RiskInfoDto checkRisk(String bizType, String obejctId, String objectType, JSONObject eventData, String operator,Boolean isAudit);
	
	/**
	 * 1. call by monitor kafka
	 * 2. audit the risk
	 * @param bizType
	 * @param obejctId
	 * @param objectType
	 * @param eventData
	 * @param operator
	 * @return
	 */
	RiskInfoDto auditRisk(String bizType, String obejctId, String objectType, JSONObject eventData, String operator,Boolean isAudit);
	
}
