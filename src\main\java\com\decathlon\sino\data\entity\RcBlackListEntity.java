package com.decathlon.sino.data.entity;

import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.Table;
import com.decathlon.sino.data.entity.basic.IdEntity;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import java.math.BigDecimal;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@SuperBuilder
@Table(name = "rc_black_list")
public class RcBlackListEntity extends IdEntity {

	private static final long serialVersionUID = 1L;

	private String bizType;
	private String objectType;
	private String objectId;
	private BigDecimal finalScore;
	private Integer riskLevel;
	private Boolean status;
	private Date expireTime;
	private Long version;
	private Date createTime;
	private Date updateTime;
	private String createBy;
	private String updateBy;

}
