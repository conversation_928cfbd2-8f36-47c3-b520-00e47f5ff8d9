package com.decathlon.sino.kafka.config;

import org.springframework.stereotype.Component;

import com.decathlon.sino.data.entity.KafkaListenerStrategyConfigEntity;

import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class KafkaTopicProvider {
    
    private final KafkaListenerService kafkaConfigService;
    
    public List<String> getTopics() {
        return kafkaConfigService.getAllStrategyConfigs().stream()
                .map(KafkaListenerStrategyConfigEntity::getTopicName)
                .toList();
    }
    
    public Map<String, List<String>> getTopicsByServer() {
        return kafkaConfigService.getAllStrategyConfigs().stream()
                .filter(KafkaListenerStrategyConfigEntity::getIsActive)
                .collect(Collectors.groupingBy(
                		KafkaListenerStrategyConfigEntity::getServerName,
                        Collectors.mapping(KafkaListenerStrategyConfigEntity::getTopicName, Collectors.toList())
                ));
    }
    
    public List<String> getTopicsForServer(String serverName) {
        return kafkaConfigService.getConfigByServer(serverName).stream()
                .map(KafkaListenerStrategyConfigEntity::getTopicName)
                .toList();
    }
} 