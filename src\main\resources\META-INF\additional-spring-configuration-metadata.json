{"properties": [{"name": "spring.database.driver-cLass-name", "type": "java.lang.String", "description": "A description for 'spring.database.driver-cLass-name'"}, {"name": "consents.support-file-types", "type": "java.lang.String", "description": "A description for 'consents.support-file-types'"}, {"name": "aop.log.enable", "type": "java.lang.String", "description": "A description for 'aop.log.enable'"}, {"name": "identity.scheduler.syncOthers.enabled", "type": "java.lang.String", "description": "A description for 'identity.scheduler.syncOthers.enabled'"}, {"name": "identity.scheduler.syncOthers.cron", "type": "java.lang.String", "description": "A description for 'identity.scheduler.syncOthers.cron'"}, {"name": "oneId.api.identity.host", "type": "java.lang.String", "description": "A description for 'oneId.api.identity.host'"}, {"name": "oneId.api.identity.exist", "type": "java.lang.String", "description": "A description for 'oneId.api.identity.exist'"}, {"name": "member.platform.consents.host", "type": "java.lang.String", "description": "A description for 'member.platform.consents.host'"}, {"name": "member.platform.store.host", "type": "java.lang.String", "description": "A description for 'member.platform.store.host'"}, {"name": "user.token.service.authorization", "type": "java.lang.String", "description": "A description for 'user.token.service.authorization'"}, {"name": "fed.token.service.url", "type": "java.lang.String", "description": "A description for 'fed.token.service.url'"}, {"name": "user.token.service.api.key", "type": "java.lang.String", "description": "A description for 'user.token.service.api.key'"}, {"name": "user.token.service.url", "type": "java.lang.String", "description": "A description for 'user.token.service.url'"}, {"name": "fed.token.service.authorization", "type": "java.lang.String", "description": "A description for 'fed.token.service.authorization'"}, {"name": "identity.scheduler.syncCalculateFragment.enabled", "type": "java.lang.String", "description": "A description for 'identity.scheduler.syncCalculateFragment.enabled'"}, {"name": "identity.scheduler.syncExecuteFragment.enabled", "type": "java.lang.String", "description": "A description for 'identity.scheduler.syncExecuteFragment.enabled'"}, {"name": "identity.scheduler.syncExecuteFragment.cron", "type": "java.lang.String", "description": "A description for 'identity.scheduler.syncExecuteFragment.cron'"}, {"name": "identity.scheduler.syncCalculateFragment.cron", "type": "java.lang.String", "description": "A description for 'identity.scheduler.syncCalculateFragment.cron'"}]}