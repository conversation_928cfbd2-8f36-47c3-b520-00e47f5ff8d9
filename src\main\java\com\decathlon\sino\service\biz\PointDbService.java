package com.decathlon.sino.service.biz;

import java.time.LocalDateTime;
import java.util.Map;

import com.decathlon.sino.data.entity.biz.AccountPointEntity;

public interface PointDbService {
	
	// query by cardNo,get the account's total point used by time range
	Integer getRangConsumer(String cardNo, LocalDateTime startTime, LocalDateTime endTime);
	
	// query by cardNo,get the account's total point produced by time range
	Integer getRangProduce(String cardNo, LocalDateTime startTime, LocalDateTime endTime);
	
	// query the new balance by cardNo after current the point change
	Integer getBlanceAfterUse(String cardNo, Integer pointChange);
	
	void save(AccountPointEntity accountPointEntity);

	AccountPointEntity getMinByCardNumber(String cardNumber);
	
	AccountPointEntity getLastByCardNumber(String cardNumber);


}
