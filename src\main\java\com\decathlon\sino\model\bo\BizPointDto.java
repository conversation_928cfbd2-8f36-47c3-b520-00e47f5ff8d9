package com.decathlon.sino.model.bo;

import java.util.List;

import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;

@Data
@JsonNaming(SnakeCaseStrategy.class)
public class BizPointDto {
	
	private String codeStatus;
	private String externalId;
	private String eventType;
	private String eventId;
	private String nickName;
	private List<PointChangDetail> pointCardMap;

}
