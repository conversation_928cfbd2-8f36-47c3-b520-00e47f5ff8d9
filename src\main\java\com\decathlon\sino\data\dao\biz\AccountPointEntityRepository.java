package com.decathlon.sino.data.dao.biz;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.Query;

import com.decathlon.sino.data.dao.basic.QueryDslBaseDao;
import com.decathlon.sino.data.entity.biz.AccountPointEntity;

public interface AccountPointEntityRepository extends QueryDslBaseDao<AccountPointEntity> {

	// query by cardNo,get the account's point change history by time range
	List<AccountPointEntity> findAllByCardNoAndCreatedAtBetween(String cardNo, LocalDateTime startTime, LocalDateTime endTime);

	// query by cardNo,get the max(id)
	AccountPointEntity getMaxIdByCardNo(String cardNo);
	
	Optional<AccountPointEntity> findByCardNoAndEventId(String cardNo, String eventId);
	
	List<AccountPointEntity> findAllByCardNo(String cardNo);
	
	@Query("SELECT MIN(a) FROM AccountPointEntity a WHERE a.cardNo = ?1")
	AccountPointEntity getMinByCardNumber(String cardNumber);
	
	@Query("SELECT a FROM AccountPointEntity a WHERE a.cardNo = ?1 ORDER BY a.createdAt DESC")
	AccountPointEntity getLastByCardNumber(String cardNumber);

}
