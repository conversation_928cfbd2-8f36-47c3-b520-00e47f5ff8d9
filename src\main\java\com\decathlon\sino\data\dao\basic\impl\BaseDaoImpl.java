package com.decathlon.sino.data.dao.basic.impl;

import com.decathlon.sino.data.entity.basic.BaseEntity;
import com.google.common.collect.Lists;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.support.JpaEntityInformation;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

import static org.apache.commons.collections4.IterableUtils.isEmpty;

public class BaseDaoImpl<T, ID> extends SimpleJpaRepository<T, ID> implements JpaRepository<T, ID> {

    private final JpaEntityInformation<T, ?> jpaEntityInformation;

    public BaseDaoImpl(JpaEntityInformation<T, ?> entityInformation, EntityManager entityManager) {
        super(entityInformation, entityManager);
        this.jpaEntityInformation = entityInformation;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public <S extends T> S save(S entity) {
        if (BaseEntity.class.isAssignableFrom(entity.getClass())) {
            BaseEntity baseEntity = (BaseEntity) entity;

            Date now = new Date();
            if (jpaEntityInformation.isNew(entity)) {
                baseEntity.setCreateTime(Optional.ofNullable(baseEntity.getCreateTime()).orElse(now));
            }
            baseEntity.setUpdateTime(now);
        }
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <S extends T> List<S> saveAll(Iterable<S> entities) {
        List<S> result = Lists.newArrayList();
        if (isEmpty(entities)) {
            return result;
        }
        Iterator<S> it = entities.iterator();
        while (it.hasNext()) {
            result.add(this.save(it.next()));
        }
        return result;
    }
}
