package com.decathlon.sino.kafka.config;

import com.decathlon.sino.data.dao.KafkaListenerStrategyConfigEntityRepository;
import com.decathlon.sino.data.entity.KafkaListenerStrategyConfigEntity;
import com.decathlon.sino.kafka.biz.KafkaListenerFactory;
import com.decathlon.sino.kafka.biz.KafkaListenerStrategy;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@AllArgsConstructor
public class KafkaListenerService {

    private final KafkaListenerStrategyConfigEntityRepository strategyConfigRepository;

    private final KafkaListenerFactory strategyFactory;

    @Transactional(readOnly = true)
    public List<KafkaListenerStrategyConfigEntity> getAllStrategyConfigs() {
        return strategyConfigRepository.findAll();
    }

    @Transactional(readOnly = true)
    public KafkaListenerStrategyConfigEntity getStrategyConfig(String clusterName, String topic) {
        return strategyConfigRepository.findByClusterNameAndTopicAndIsActiveTrue(clusterName, topic);
    }
    
    public  List<KafkaListenerStrategyConfigEntity> getStrategyConfigByTopic( String topic) {
        return strategyConfigRepository.findAllByTopicAndIsActiveTrue(topic);
    }

    @Transactional
    public KafkaListenerStrategyConfigEntity saveStrategyConfig(KafkaListenerStrategyConfigEntity config) {
        return strategyConfigRepository.save(config);
    }

    @Transactional
    public void deactivateStrategyConfig(String clusterName, String topic) {
    	KafkaListenerStrategyConfigEntity config = strategyConfigRepository.findByClusterNameAndTopicAndIsActiveTrue(clusterName, topic);
        if (config != null) {
            config.setIsActive(false);
            strategyConfigRepository.save(config);
        }
    }

    public Map<String, KafkaListenerStrategy> getAllStrategies() {
        return strategyFactory.getAllStrategies();
    }

    public KafkaListenerStrategy getStrategy(String strategyName) {
        return strategyFactory.getStrategy(strategyName);
    }

	public List<KafkaListenerStrategyConfigEntity> getConfigByServer(String serverName) {
		return strategyConfigRepository.findAllByServerNameAndIsActiveTrue(serverName);
	}
	
	public KafkaListenerStrategyConfigEntity getKafkaConfigByServer(String serverName) {
		return strategyConfigRepository.findByServerNameAndIsActiveTrue(serverName);
	}

} 
