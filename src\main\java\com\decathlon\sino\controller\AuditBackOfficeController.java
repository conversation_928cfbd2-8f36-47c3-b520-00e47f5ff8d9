package com.decathlon.sino.controller;

import java.util.List;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.decathlon.sino.common.annotations.StartSwaggerScan;
import com.decathlon.sino.common.config.PageResultDTO;
import com.decathlon.sino.model.bo.ImportRow;
import com.decathlon.sino.model.criteria.ListDataSearchCriteria;
import com.decathlon.sino.model.ouput.RiskInfoOutput;
import com.decathlon.sino.service.AuditBackOfficeService;

import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@StartSwaggerScan
@RestController
@Slf4j
@AllArgsConstructor
public class AuditBackOfficeController {
	
	private final AuditBackOfficeService auditBackOfficeService;
	
	/**
	 * Import risk list data by type
	 * @param importListDto
	 * @param bizType
	 * @return
	 */
	@PostMapping(value = "/imports")
    @ApiOperation(value = "import risk list")
    public String importRiskList(@RequestBody List<ImportRow> data, @RequestParam(value = "biz_type") String bizType, @RequestParam String operator,@RequestParam Boolean status) {
    	return auditBackOfficeService.importRiskList(data,bizType,operator,status);
    }
	
	@DeleteMapping(value = "/imports")
    @ApiOperation(value = "import risk list")
    public String delete(@RequestParam(value = "person_id") Long personId, @RequestParam(value = "biz_type") String bizType, @RequestParam String operator,@RequestParam Boolean status) {
    	return auditBackOfficeService.deleteRiskList(personId,bizType,operator,status);
    }
    
	/**
	 * Query risk list by type
	 * @param bizType
	 * @return
	 */
    @GetMapping(value = "/user/risk")
    @ApiOperation(value = "query risk list") 
    public PageResultDTO<ImportRow> queryRisks(@RequestParam(value = "biz_type",required = false ) String bizType,@RequestParam(value = "card_number",required = false ) String cardNumber,@RequestParam(value = "person_id",required = false ) Long personId,@RequestParam(value = "mobile",required = false ) String mobile, ListDataSearchCriteria search) {
    	search.setBizType(bizType);
    	search.setCardNumber(cardNumber);
    	search.setPersonId(personId);
    	search.setMobile(mobile);
    	return auditBackOfficeService.queryRisks(search);
    }

    /**
	 * Query risk statistics by type
	 * @param bizType
	 */
    @GetMapping(value = "/user/risk/statics")
    @ApiOperation(value = "statics risk list")
    public List<RiskInfoOutput> riskStatics(@RequestParam(value = "biz_type") String bizType) {
    	// 处理相关的统计
    	return auditBackOfficeService.riskStatics(bizType);
    }
    
    /**
	 * Refine import data from file
	 * @param file
	 * @return
	 */
    @PostMapping(value = "/imports/refines")
    @ApiOperation(value = "refine import list")
    public List<ImportRow> refineImportDatas(@RequestBody MultipartFile file) {
    	return auditBackOfficeService.refineImportData(file);
    }
    
    @PostMapping(value = "/imports/refine")
    @ApiOperation(value = "refine import list")
    public ImportRow refineImportData(@RequestBody ImportRow importRow) {
    	return auditBackOfficeService.refineData(importRow);
    }


}
