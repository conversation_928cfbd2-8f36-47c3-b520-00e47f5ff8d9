package com.decathlon.sino.common.aop;


import com.decathlon.sino.common.util.AES256Util;
import com.decathlon.sino.common.util.JsonUtil;
import com.decathlon.sino.common.util.LocalBase64Utils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.mvc.method.annotation.ExtendedServletRequestDataBinder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpServletResponseWrapper;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Aspect
@Component
@ConditionalOnProperty("aop.log.enable")
@Order(1)
@Slf4j
public class ControllerLogAspect {

    @Pointcut("execution(* com.decathlon.sino.controller..*.*(..))")
    private void controllerPointCut() {
        log.info("point cut");
    }

    @Before("controllerPointCut()")
    public void doBefore(JoinPoint joinPoint) {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        if (!"ping".equals(joinPoint.getSignature().getName())) {
            String uri = request.getRequestURI();
            String method = request.getMethod();
            if(!uri.contains("imports/refine")) {
				String args = JsonUtil.toJson(joinPoint.getArgs());
				log.info("接口请求-【{}】{}请求, Args:{}", method, uri, args);
			}
        }
    }

    @Around("controllerPointCut()")
    public Object calculateInterTime(ProceedingJoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        long startTime = System.currentTimeMillis();
        Object result = joinPoint.proceed();
        if (!"ping".equals(joinPoint.getSignature().getName())) {
            Map<String, Object> paramMap = fetchParamInfo(joinPoint);
            log.info("URL:{} ,Time-Consuming : {} ms,HTTP Method:{},Class Method:{}.{},Request Args:{}Response Args:{}", request.getRequestURL().toString(), System.currentTimeMillis() - startTime, request.getMethod(), joinPoint.getSignature().getDeclaringTypeName(), joinPoint.getSignature().getName(), SensitiveJsonUtil.toJsonString(paramMap), SensitiveJsonUtil.toJsonString(result));
        }
        return result;
    }

    private Map<String, Object> fetchParamInfo(ProceedingJoinPoint joinPoint) {
        ObjectMapper mapper = new ObjectMapper();
        Object[] objs = joinPoint.getArgs();
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        String[] argNames = methodSignature.getParameterNames();
        Map<String, Object> paramMap = new HashMap<>();
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        for (int i = 0; i < objs.length; i++) {
            if (!"file".equals(argNames[i]) && !"httpServletRequest".equals(argNames[i]) && objs[i] != null && !(objs[i] instanceof ExtendedServletRequestDataBinder) && !(objs[i] instanceof HttpServletResponseWrapper) && !(objs[i] instanceof HttpServletRequest) && !(objs[i] instanceof HttpServletResponse) && !(objs[i] instanceof Throwable)) {
                if ("mobile".equals(argNames[i]) || "email".equals(argNames[i])) {
                    paramMap.put(argNames[i], AES256Util.encode(LocalBase64Utils.md516Lower("MPMSTORE"), objs[i].toString()));
                } else {
                    paramMap.put(argNames[i], objs[i]);
                }
            }
        }
        return paramMap;
    }
}
