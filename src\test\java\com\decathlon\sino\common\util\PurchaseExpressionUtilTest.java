package com.decathlon.sino.common.util;

import org.junit.runner.RunWith;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.junit.jupiter.api.Test;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.*;

import com.decathlon.sino.App;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringJUnit4ClassRunner.class)
@EnableAutoConfiguration
@SpringBootTest(classes = App.class)
@Slf4j
public class PurchaseExpressionUtilTest {

	@Test
	public void testOrderChannelInListWithSpEL() {
		List<String> channelList = Arrays.asList("MP&官网", "Tmall", "JD", "抖音", "PDD", "线下门店", "美团", "JDDJ");
		String orderChannel = "JD";

		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("orderChannel", orderChannel);
		context.setVariable("channelList", channelList);

		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#channelList.contains(#orderChannel)").getValue(context, Boolean.class);

		assertTrue(result);
	}

	@Test
	public void testOrderAmountGreaterThan5000WithSpEL() {
		double orderAmount = 6000.0;

		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("orderAmount", orderAmount);

		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#orderAmount > 5000").getValue(context, Boolean.class);

		assertTrue(result);
	}

	@Test
	public void testPurchaseQuantityGreaterThan10WithSpEL() {
		int quantity = 15;
		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("quantity", quantity);
		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#quantity > 10").getValue(context, Boolean.class);
		assertTrue(result);
	}

	@Test
	public void testLineAmountGreaterThan2000WithSpEL() {
		double lineAmount = 2500.0;
		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("lineAmount", lineAmount);
		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#lineAmount > 2000").getValue(context, Boolean.class);
		assertTrue(result);
	}

	@Test
	public void testAddressLengthLessThan8WithSpEL() {
		String address = "北京路";
		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("address", address);
		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#address != null && #address.length() < 8").getValue(context, Boolean.class);
		assertTrue(result);
	}

	@Test
	public void testAddressLengthLessThan1WithSpEL() {
		String address = "";
		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("address", address);
		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#address != null && #address.length() < 1").getValue(context, Boolean.class);
		assertTrue(result);
	}

	@Test
	public void testRegisterDateGreaterThan365DaysWithSpEL() {
		java.time.LocalDate registerDate = java.time.LocalDate.now().minusDays(400);
		int days = 365;
		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("registerDate", registerDate);
		context.setVariable("days", days);
		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#registerDate.isBefore(T(java.time.LocalDate).now().minusDays(#days))").getValue(context, Boolean.class);
		assertTrue(result);
	}

	@Test
	public void testOrderCountGreaterThan10WithSpEL() {
		int orderCount = 15;
		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("orderCount", orderCount);
		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#orderCount > 10").getValue(context, Boolean.class);
		assertTrue(result);
	}

	@Test
	public void testNetOrderAmountGreaterThan3000WithSpEL() {
		double netOrderAmount = 3500.0;
		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("netOrderAmount", netOrderAmount);
		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#netOrderAmount > 3000").getValue(context, Boolean.class);
		assertTrue(result);
	}

}
