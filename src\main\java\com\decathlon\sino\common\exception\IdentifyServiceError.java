package com.decathlon.sino.common.exception;


import lombok.Getter;

@Getter
public enum IdentifyServiceError implements BaseError {

    ACCOUNT_NOT_FOUND("4001", "account not found"),
    ACCOUNT_PASSWORD_WRONG("4002", "account password wrong"),
    ACCOUNT_HAD_EXIST("4003", "account had exist"),
    CARD_NUMBER_POOL_ABNORMAL("4004", "card number pool abnormal"),
    ACCOUNT_ID_BLANK("4005", "account id is blank"),
    EMAIL_HAS_USED("5161", "Email already used by another customer"),
    ACCOUNT_DELETE_FAILED_IN_1ID("6001", "account delete failed in 1ID"),
    MOBILE_HAS_USED("4006", "Mobile already used by another customer"),
    PASSWORD_SAME("5065", "Forbidden password");

    private final String code;

    private final String message;

    IdentifyServiceError(String code, String message) {
        this.code = code;
        this.message = message;
    }

}