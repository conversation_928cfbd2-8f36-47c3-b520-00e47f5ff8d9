package com.decathlon.sino.model.biz;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @create 2018-06-04
 */
@ApiModel(value = "item_info",
    description = "the item information in a post date to be used in point calculate ")
public class ItemInfoBpn implements Serializable {

  private static final long serialVersionUID = -575546493160459142L;

  @JsonProperty(value = "item_id")
  private String itemId;

  @JsonProperty(value = "quantity")
  private Integer quantity;

  @JsonProperty(value = "price")
  private Double price;

  @JsonProperty(value = "item_total_price")
  private Double itemTotalPrice;

  @JsonProperty(value = "line_number")
  private String lineNumber;

  @JsonProperty(value = "in_black_list")
  private Boolean inBlacklist = false;

  @JsonProperty(value = "origin_transaction_id")
  private String originTransactionId;


  public ItemInfoBpn() {
  }


  public String getItemId() {
    return itemId;
  }


  public void setItemId(String itemId) {
    this.itemId = itemId;
  }


  public Integer getQuantity() {
    return quantity;
  }


  public void setQuantity(Integer quantity) {
    this.quantity = quantity;
  }


  public Double getPrice() {
    return price;
  }


  public void setPrice(Double price) {
    this.price = price;
  }


  public Double getItemTotalPrice() {
    return itemTotalPrice;
  }


  public void setItemTotalPrice(Double itemTotalPrice) {
    this.itemTotalPrice = itemTotalPrice;
  }


  public String getLineNumber() {
    return lineNumber;
  }


  public void setLineNumber(String lineNumber) {
    this.lineNumber = lineNumber;
  }


  public Boolean getInBlacklist() {
    return inBlacklist;
  }


  public void setInBlacklist(Boolean inBlacklist) {
    this.inBlacklist = inBlacklist;
  }


  public void setOriginTransactionId(String originTransactionId) {
    this.originTransactionId = originTransactionId;
  }

  public String getOriginTransactionId() {
    return this.originTransactionId;
  }

  public static long getSerialversionuid() {
    return serialVersionUID;
  }

}
