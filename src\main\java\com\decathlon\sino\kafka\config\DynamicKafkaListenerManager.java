package com.decathlon.sino.kafka.config;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.listener.AcknowledgingMessageListener;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.stereotype.Component;

import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@AllArgsConstructor
public class DynamicKafkaListenerManager implements InitializingBean, DisposableBean {

    private final KafkaTopicProvider topicProvider;
    private final KafkaContainerFactoryProvider factoryProvider;
    private final AcknowledgingMessageListener<String, Object> messageListener;
    
    private final Map<String, List<ConcurrentMessageListenerContainer<String, Object>>> containers = new ConcurrentHashMap<>();

    @Override
    public void afterPropertiesSet() {
        refreshListeners();
    }

    public synchronized void refreshListeners() {
    	Map<String, List<String>> currentTopics = topicProvider.getTopicsByServer();
        
        // Remove listeners for topics that no longer exist
        containers.keySet().stream()
                .filter(server -> !currentTopics.containsKey(server))
                .forEach(this::removeListener);
        
        // Add listeners for new topics
        currentTopics.keySet().stream()
                .filter(server -> !containers.containsKey(server))
                .forEach(this::addListener);
    }

    public void addListener(String server) {
        ConcurrentKafkaListenerContainerFactory<String, Object> factory = 
                factoryProvider.getContainerFactory(server);
        
        List<String> topics = topicProvider.getTopicsForServer(server);
        List<ConcurrentMessageListenerContainer<String, Object>> serverContainers = new ArrayList<>();
        topics.forEach(topic -> {
        	ConcurrentMessageListenerContainer<String, Object> container = 
        			factory.createContainer(topic);
        	container.getContainerProperties().setMessageListener(messageListener);
        	container.setBeanName("dynamic-container-" + topic+"-" + server);
        	container.start();
        	serverContainers.add(container);
		});
        containers.put(server, serverContainers);
    }

    public void removeListener(String server) {
        List<ConcurrentMessageListenerContainer<String, Object>>  container = containers.remove(server);
        for (ConcurrentMessageListenerContainer<String, Object> concurrentMessageListenerContainer : container) {
        	if (concurrentMessageListenerContainer != null) {
        		concurrentMessageListenerContainer.stop();
        	}
		}
    }

    @Override
    public void destroy() {
    	containers.values().forEach(containers -> {
			for (ConcurrentMessageListenerContainer<String, Object> container : containers) {
				if (container != null) {
					container.stop();
				}
			}
		});
        containers.clear();
    }
}