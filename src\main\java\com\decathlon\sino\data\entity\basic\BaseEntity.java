package com.decathlon.sino.data.entity.basic;


import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.TypeDef;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
@MappedSuperclass
@TypeDef(name = "json", typeClass = JsonStringType.class)
@TypeDef(name = "jsonb", typeClass = JsonBinaryType.class)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@ToString(callSuper = true)
@EntityListeners(AuditingEntityListener.class)
public class BaseEntity extends IdEntity {

    private static final long serialVersionUID = -7350710703091819091L;

    @Column(name = "create_time", updatable = false)
    protected Date createTime;

    @Column(name = "update_time")
    protected Date updateTime;
}
