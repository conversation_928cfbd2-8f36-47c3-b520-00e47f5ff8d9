package com.decathlon.sino.controller;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.decathlon.sino.common.annotations.StartSwaggerScan;
import com.decathlon.sino.model.input.RiskInfoDto;
import com.decathlon.sino.service.AuditBizFactory;
import com.decathlon.sino.service.AuditService;

import cn.hutool.json.JSONObject;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@StartSwaggerScan
@RestController
@Slf4j
@AllArgsConstructor
public class AuditController {
	
	private final  AuditBizFactory auditBizFactory;
	
    @PostMapping(value = "/risk")
    @ApiOperation(value = "check risk")
    public RiskInfoDto checkRisk(@RequestParam String bizType,@RequestParam String obejctId, @RequestParam String objectType, @RequestBody JSONObject eventData, @RequestParam String operator,@RequestParam Boolean isAudit) {
    	AuditService auditService =  auditBizFactory.getAuditService(bizType,objectType);
    	return auditService.checkRisk(bizType, obejctId, objectType, eventData, operator, isAudit);
    }


}
