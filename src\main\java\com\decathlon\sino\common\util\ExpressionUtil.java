package com.decathlon.sino.common.util;

import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ExpressionUtil {
	
	private static final ExpressionParser parser = new SpelExpressionParser();
    private static final Map<String, Expression> expressionCache = new ConcurrentHashMap<>();
	
	ExpressionUtil() {
		// Prevent instantiation
	}
    /**
     * used SpEL analysis expression and calculate result
     */
    public static BigDecimal evaluateExpression(String expr, Map<String, Object> vars) {
        try {
            // query expression cache
            Expression expression = expressionCache.computeIfAbsent(expr, parser::parseExpression);
            
            // create evaluation context
            StandardEvaluationContext context = new StandardEvaluationContext();
            vars.forEach(context::setVariable);
            
            // calculate expression
            Object result = expression.getValue(context);
            
            // transform result to BigDecimal
            if (result instanceof Number) {
                return new BigDecimal(result.toString());
            } else if (result instanceof String) {
                return new BigDecimal((String) result);
            }else if(result instanceof Boolean) {
            	if (Boolean.TRUE.equals(result)) {
					return BigDecimal.ONE;
				 }else {
					 return BigDecimal.ZERO; 
				 }
            } else {
                throw new IllegalArgumentException("parse result error: " + result);
            }
            
        } catch (Exception e) {
            log.error("Expression exception : expr={}, error={}", expr, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }
    
    /**
     * validate expression
     */
    public boolean validateExpression(String expr) {
        try {
            parser.parseExpression(expr);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * clear expression cache
     */
    public void clearExpressionCache() {
        expressionCache.clear();
    }
    

}
