package com.decathlon.sino.kafka.config;

import java.util.List;

import lombok.SneakyThrows;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.listener.AcknowledgingMessageListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import com.decathlon.sino.data.entity.KafkaListenerStrategyConfigEntity;
import com.decathlon.sino.kafka.biz.KafkaListenerFactory;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@AllArgsConstructor
public class KafkaMessageListener implements AcknowledgingMessageListener<String, Object> {
	
	
	private final KafkaListenerFactory kafkaListenerStrategyFactory;
	
    private final KafkaListenerService kafkaListenerService;

	@SneakyThrows
    @Override
	public void onMessage(ConsumerRecord<String, Object> data, Acknowledgment acknowledgment) {
		 String topic = data.topic();
		 log.info("topic: {}, partition: {}, offset: {}, key: {}, value: {}", topic, data.partition(), data.offset(), data.key(), data.value());
		 List<KafkaListenerStrategyConfigEntity> kafkaListenerStrategyConfigEntities =  kafkaListenerService.getStrategyConfigByTopic(topic);
		 if (acknowledgment!=null) {
			 acknowledgment.acknowledge();
		 }
		 for (KafkaListenerStrategyConfigEntity kafkaListenerStrategyConfigEntity : kafkaListenerStrategyConfigEntities) {
			 String strategyName =  kafkaListenerStrategyConfigEntity.getStrategyName();
			 kafkaListenerStrategyFactory.getStrategy(strategyName).handleMessage(data);
		}
		 
	}

    

} 