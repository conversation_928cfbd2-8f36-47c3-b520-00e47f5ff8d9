package com.decathlon.sino.kafka.biz;

import org.springframework.stereotype.Component;

import lombok.AllArgsConstructor;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@AllArgsConstructor
public class KafkaListenerFactory {
	
    private final Map<String, KafkaListenerStrategy> strategyMap = new HashMap<>();

    private final  List<KafkaListenerStrategy> strategies;
    

    @PostConstruct
    public void init() {
        for (KafkaListenerStrategy bizName : strategies) {
            strategyMap.put(bizName.getBizName(), bizName);
        }
    }

    public KafkaListenerStrategy getStrategy(String bizName) {
        KafkaListenerStrategy strategy = strategyMap.get(bizName);
        if (strategy == null) {
            throw new IllegalArgumentException("Strategy not found: " + bizName);
        }
        return strategy;
    }

    public Map<String, KafkaListenerStrategy> getAllStrategies() {
        return new HashMap<>(strategyMap);
    }
    
} 
