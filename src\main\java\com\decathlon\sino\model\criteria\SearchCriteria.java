package com.decathlon.sino.model.criteria;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;

import com.decathlon.sino.common.constant.Constants;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static org.springframework.data.domain.Sort.Order.asc;
import static org.springframework.data.domain.Sort.Order.desc;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SearchCriteria implements Serializable {

    private static final long serialVersionUID = -8061943589646217110L;
    protected Map<String, Direction> sortMap = new LinkedHashMap<>();
    private PageRequest pageRequest;
    private Integer page = Constants.DEFAULT_PAGE_NUMBER;

    private Integer size = Constants.DEFAULT_PAGE_SIZE;

    private Direction sortDir;
    private String sortBy;

    public Pageable getPageable() {
        int pageNumber = Math.max(this.page - 1, 0);
        Map<String, Direction> sortMap = this.getSortMap();
        if (StringUtils.isNotBlank(sortBy)) {
            sortMap.put(sortBy, sortDir == null ? Direction.ASC : sortDir);
        }
        this.pageRequest = Optional.ofNullable(getSortMap())
            .filter(map -> map.size() > 0)
            .map(sort -> {
                List<Order> orders = getSortMap().entrySet().stream()
                    .map(entry -> entry.getValue() == Direction.ASC ? asc(entry.getKey()) : desc(entry.getKey()))
                    .collect(Collectors.toList());
                return PageRequest.of(pageNumber, size, Sort.by(orders));
            }).orElseGet(() -> PageRequest.of(pageNumber, size, Sort.by("id")));

        return this.pageRequest;
    }
}
