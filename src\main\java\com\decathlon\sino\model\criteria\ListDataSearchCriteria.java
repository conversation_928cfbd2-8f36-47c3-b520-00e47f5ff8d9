package com.decathlon.sino.model.criteria;

import org.apache.commons.lang3.StringUtils;

import com.decathlon.sino.data.entity.QRcListImportEntity;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Predicate;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class ListDataSearchCriteria extends SearchCriteria implements Criteria {
	
	private String bizType;
	private Boolean status;
	private Long personId;
	private String cardNumber;
	private String mobile;
	

	private static final long serialVersionUID = 1L;

	@Override
	public Predicate toPredicate() {
		QRcListImportEntity rcListImportEntity = QRcListImportEntity.rcListImportEntity;
		Predicate predicate = rcListImportEntity.isNotNull();
		
        if (StringUtils.isNotBlank(bizType)) {
            predicate = StringUtils.isNotBlank(bizType) ? ExpressionUtils.and(predicate, rcListImportEntity.type.equalsIgnoreCase(bizType)) : predicate;
        }
        
        if (StringUtils.isNotBlank(mobile)) {
            predicate = StringUtils.isNotBlank(mobile) ? ExpressionUtils.and(predicate, rcListImportEntity.mobile.equalsIgnoreCase(mobile)) : predicate;
        }
        
        if (StringUtils.isNotBlank(cardNumber)) {
            predicate = StringUtils.isNotBlank(cardNumber) ? ExpressionUtils.and(predicate, rcListImportEntity.cardNumber.equalsIgnoreCase(cardNumber)) : predicate;
        }
        
        if (personId != null) {
			predicate = ExpressionUtils.and(predicate, rcListImportEntity.personId.eq(personId));
		}
        
        if (status != null) {
			predicate = ExpressionUtils.and(predicate, rcListImportEntity.status.eq(status));
		}
        
		return predicate;
	}

}
