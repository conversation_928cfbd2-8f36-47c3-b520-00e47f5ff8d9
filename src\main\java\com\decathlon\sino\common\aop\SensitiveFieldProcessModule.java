package com.decathlon.sino.common.aop;

import com.decathlon.sino.common.util.AES256Util;
import com.decathlon.sino.common.util.LocalBase64Utils;
import com.fasterxml.jackson.annotation.JacksonAnnotation;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.Version;
import com.fasterxml.jackson.core.util.VersionUtil;
import com.fasterxml.jackson.databind.Module;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.ser.BeanPropertyWriter;
import com.fasterxml.jackson.databind.ser.BeanSerializerModifier;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.ArrayList;
import java.util.List;

public class SensitiveFieldProcessModule extends Module {

    private static final String MODULE_NAME = "jackson-sensitive-field-process-module";

    private Version version = VersionUtil.parseVersion("0.0.1", "com.example", MODULE_NAME);

    @Override
    public String getModuleName() {
        return MODULE_NAME;
    }

    @Override
    public Version version() {
        return version;
    }

    @Override
    public void setupModule(SetupContext setupContext) {
        setupContext.addBeanSerializerModifier(new SensitiveFieldModifier());
    }

    public static class SensitiveFieldModifier extends BeanSerializerModifier {

        @Override
        public List<BeanPropertyWriter> changeProperties(SerializationConfig config, BeanDescription beanDesc, List<BeanPropertyWriter> beanProperties) {
            List<BeanPropertyWriter> newWriters = new ArrayList<>();
            for (BeanPropertyWriter writer : beanProperties) {
                if (writer.getAnnotation(Sensitive.class) != null && writer.getType().isTypeOrSubTypeOf(String.class)) {
                    // 如果带有 @Sensitive 注解，并且是字符串，则使用自定义处理
                    JsonSerializer<Object> serializer = new SensitiveJsonSerializer(writer.getSerializer());
                    writer.assignSerializer(serializer);

                }
                newWriters.add(writer);
            }

            return newWriters;

        }
    }

    @JacksonAnnotation
    @Retention(RetentionPolicy.RUNTIME)
    @Target({ElementType.FIELD})
    public @interface Sensitive {
    }

    public static class SensitiveJsonSerializer extends JsonSerializer<Object> {

        private final JsonSerializer<Object> serializer;

        public SensitiveJsonSerializer(JsonSerializer<Object> serializer) {
            this.serializer = serializer;
        }

        @Override
        public void serialize(Object object, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException, JsonProcessingException {
            if (object instanceof String) {
                String str = (String) object;
                if (StringUtils.isNotBlank(str)) {
                    object = processSensitiveField(str);
                }
            }
            if (this.serializer == null) {
                serializerProvider.defaultSerializeValue(object, jsonGenerator);
            } else {
                this.serializer.serialize(object, jsonGenerator, serializerProvider);
            }
        }

        private static String processSensitiveField(String input) {

            input = StringUtils.trim(input);
            int strLen = input.length();
            if (strLen > 0) {
                return AES256Util.encode(LocalBase64Utils.md516Lower("MPMSTORE"), input);
            } else {
                return input;
            }
        }
    }

}