package com.decathlon.sino.common.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import lombok.Getter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;

public class JsonUtil {

    private static final Logger logger = LogManager.getLogger(JsonUtil.class);

    @Getter
    private static final ObjectMapper mapper = new ObjectMapper();

    static {
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);

        //允许使用未带引号的字段名
        mapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        //允许使用单引号
        mapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS"));
    }

    /**
     * 读取json文件
     * @return 返回json字符串
     */
    public static String readJsonFile(File jsonFile) {
        String jsonStr = "";
        logger.info("————开始读取" + jsonFile.getPath() + "文件————");
        Reader reader = null;
        try {
            //File jsonFile = new File(fileName);
            reader = new InputStreamReader(new FileInputStream(jsonFile), StandardCharsets.UTF_8);
            int ch = 0;
            StringBuilder sb = new StringBuilder();
            while ((ch = reader.read()) != -1) {
                sb.append((char) ch);
            }
            jsonStr = sb.toString();
            logger.info("————读取" + jsonFile.getPath() + "文件结束!————");
            return jsonStr;
        } catch (Exception e) {
            logger.error("————读取" + jsonFile.getPath() + "文件出现异常，读取失败!————");
            logger.error(e.getMessage(), e);
            return null;
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    logger.error(e.getMessage());
                }
            }
        }
    }

    /**
     * the object serial to json
     * @param value
     * @return
     */
    public static String toJson(Object value) {
        String json = null;
        try {
            json = mapper.writeValueAsString(value);
        } catch (JsonProcessingException e) {
            logger.error("JSON serialization error:", e);
        }
        return json;
    }

    /**
     * the json string deserialization to object
     * @param json
     * @param clazz
     * @return
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        T result = null;
        try {
            result = mapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            logger.error("JSON deserialization error:", e);
        }
        return result;
    }


    /**
     * the json string deserialization to object
     * @param json
     * @param typeReference
     * @return
     */
    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        T result = null;
        try {
            result = mapper.readValue(json, typeReference);
        } catch (JsonProcessingException e) {
            logger.error("JSON deserialization error:", e);
        }
        return result;
    }

    /**
     * the json string deserialization to object
     * @param json
     * @param parametrized
     * @param parameterClasses
     * @param <T>
     * @return
     */
    public static <T> T fromJson(String json, Class<?> parametrized, Class<?>... parameterClasses) {
        T result = null;
        JavaType type = mapper.getTypeFactory().constructParametricType(parametrized, parameterClasses);
        try {
            result = mapper.readValue(json, type);
        } catch (JsonProcessingException e) {
            logger.error("JSON deserialization error:", e);
        }
        return result;
    }

    public static JsonNode toJsonNode(Object value) {
        return mapper.valueToTree(toJson(value));
    }

}