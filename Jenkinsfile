#!/usr/bin/env groovy
@Library('custom-lib@feat_cn_methods_k8s') _

def args = [:]
if (env.BRANCH_NAME == 'main') {
    args.targetEnv = 'production'
    args.releaseType= 'RELEASE'
} else {
    args.targetEnv = 'preprod'
    args.releaseType= 'SNAPSHOT'
}

args.image_name = 'member_platform_cn/sino_aud'
args.jdk = 'java17'
args.maven = 'maven3.6'
args.email_to = '<EMAIL>'

args.proxy_host = 'proxy-internet-azure-cn.dktapp.cloud'
args.no_proxy_host = 'localhost|127.0.0.1|*.subsidia.org'
args.registry_local = 'acrcsz.azurecr.cn'
args.registry = 'acrcsz.azurecr.cn'

args.build_command = "mvn clean install -Drelease.type=${args.releaseType} -Ddockerfile.skip -Djacoco.skip=true -Dhttp.proxyPort=3128 -Dhttp.proxyHost=proxy-internet-azure-cn.dktapp.cloud -Dhttps.proxyPort=3128 -Dhttps.proxyHost=proxy-internet-azure-cn.dktapp.cloud -Dhttp.nonProxyHosts='localhost,127.0.0.1,*.subsidia.org,*.dktapp.cloud'"

pipeline_java(args)
