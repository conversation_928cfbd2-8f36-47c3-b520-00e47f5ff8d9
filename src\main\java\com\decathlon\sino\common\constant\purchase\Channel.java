package com.decathlon.sino.common.constant.purchase;

import lombok.AllArgsConstructor;
import lombok.Data;


@AllArgsConstructor
public enum Channel {

    MPM_DCN("50-46-46","MP&官网"),
     TMALL("50-188-188","天猫"),
     J<PERSON>("50-244-244","京东"),
     DOUYIN("50-269-269","抖音"),
     MEITUAN("50-305-305","美团"),
     JDDJ("50-279-279","京东到家"),
     PHYSICAL("7","线下")
     ;


     private String storeNumberPrefix;

     private String name;


    public String getStoreNumberPrefix() {
        return this.storeNumberPrefix;
    }

    public String getName() {
        return this.name;
    }

    public static Channel getByPrefix(String storeNumberPrefix) {
        for (Channel chanel : Channel.values()) {
            if (chanel.getStoreNumberPrefix().equals(storeNumberPrefix)) {
                return chanel;
            }
        }
        return null;
    }

}
