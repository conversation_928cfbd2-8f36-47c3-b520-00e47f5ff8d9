package com.decathlon.sino.common.config;

import com.decathlon.sino.common.constant.Constants;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.deser.std.DateDeserializers;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.DateSerializer;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import org.springframework.boot.jackson.JsonComponentModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;

@Configuration
public class CommonObjectMapperConfig {

    public static ObjectMapper generateObjectMapper() {
        return getDefaultObjectMapper(false);
    }

    public static ObjectMapper generateObjectMapperWithTZ() {
        return getDefaultObjectMapper(true);
    }

    public static ObjectMapper withXss(ObjectMapper mapper) {
        SimpleModule xssModule = new SimpleModule("XssStringJsonModule");
        xssModule.addDeserializer(String.class, new JacksonXssDeserializer());
        xssModule.addSerializer(String.class, new JacksonXssSerializer());
        mapper.registerModule(xssModule);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        return mapper;
    }

    public static ObjectMapper withNamingStrategy(ObjectMapper mapper, PropertyNamingStrategy namingStrategy) {
        mapper.setPropertyNamingStrategy(namingStrategy);
        return mapper;
    }

    private static ObjectMapper getDefaultObjectMapper(boolean withTZ) {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        mapper.registerModule(new Jdk8Module());
        mapper.registerModule(new ParameterNamesModule());
        mapper.registerModule(new JsonComponentModule());
        mapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

        JavaTimeModule javaTimeModule = new JavaTimeModule();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(Constants.DateTimePattern.FMT_DATE_TIME);

        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(dateFormatter));
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateFormatter));
        javaTimeModule.addDeserializer(LocalDate.class, new LocalDateDeserializer(dateFormatter));
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(dateFormatter));

        javaTimeModule.addSerializer(Date.class, new DateSerializer(false, new SimpleDateFormat(Constants.DateTimePattern.FMT_DATE_TIME)));
        javaTimeModule.addDeserializer(Date.class, new DateDeserializers.DateDeserializer());

        mapper.registerModule(javaTimeModule);
        return mapper;
    }

    @Bean("commonObjectMapper")
    public ObjectMapper objectMapper() {
        return generateObjectMapper();
    }
}
