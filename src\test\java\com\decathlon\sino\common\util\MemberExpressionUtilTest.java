package com.decathlon.sino.common.util;

import com.decathlon.sino.data.entity.biz.PurchaseEntity;
import org.junit.runner.RunWith;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.junit.jupiter.api.Test;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

import com.decathlon.sino.App;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringJUnit4ClassRunner.class)
@EnableAutoConfiguration
@SpringBootTest(classes = App.class)
@Slf4j
public class MemberExpressionUtilTest {
	
//	@Test
	public void testUsualStoreInListWithSpEL() {
		
		List<String> storeList = Arrays.asList("MP&官网", "Tmall", "JD", "抖音", "PDD", "线下门店", "美团", "JDDJ");
		String store = "Tmalls";

		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("store", store);
		context.setVariable("storeList", storeList);

		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#storeList.contains(#store)").getValue(context, Boolean.class);

		assertTrue(result);
	}

//	@Test
	public void testIsRecentUserWithSpEL() {
		LocalDate registerDate = LocalDate.now().minusDays(5);

		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("registerDate", registerDate);

		ExpressionParser parser = new SpelExpressionParser();
		// 30天内注册
		Boolean result = parser.parseExpression("T(java.time.LocalDate).now().minusDays(30).isBefore(#registerDate)").getValue(context, Boolean.class);

		assertTrue(result);
	}

//	@Test
	public void testIsRegisterWithinDaysWithSpEL() {
		LocalDate registerDate = LocalDate.now().minusDays(10);
		int days = 30;

		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("registerDate", registerDate);
		context.setVariable("days", days);

		ExpressionParser parser = new SpelExpressionParser();
		// 注册时间小于days天
		Boolean result = parser.parseExpression("T(java.time.LocalDate).now().minusDays(#days).isBefore(#registerDate)").getValue(context, Boolean.class);

		assertTrue(result);
	}

//	@Test
	public void testIsRegisterGreaterThan365DaysWithSpEL() {
		LocalDate registerDate = LocalDate.now().minusDays(400);
		int days = 365;

		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("registerDate", registerDate);
		context.setVariable("days", days);

		ExpressionParser parser = new SpelExpressionParser();
		// 注册时间大于days天
		Boolean result = parser.parseExpression("#registerDate.isBefore(T(java.time.LocalDate).now().minusDays(#days))").getValue(context, Boolean.class);

		assertTrue(result);
	}

//	@Test
	public void testNameNotEmptyWithSpEL() {
		String name = "张三";
		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("name", name);
		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#name != null && #name != ''").getValue(context, Boolean.class);
		assertTrue(result);
	}

//	@Test
	public void testGenderNotEmptyWithSpEL() {
		String gender = "男";
		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("gender", gender);
		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#gender != null && #gender != ''").getValue(context, Boolean.class);
		assertTrue(result);
	}

//	@Test
	public void testBirthdayNotEmptyWithSpEL() {
		LocalDate birthday = LocalDate.of(1990, 1, 1);
		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("birthday", birthday);
		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#birthday != null").getValue(context, Boolean.class);
		assertTrue(result);
	}

	@Test
	public void testAllFieldsNotEmptyWithSpEL() {
		String name = "张三";
		String gender = "男";
		LocalDate birthday = LocalDate.of(1990, 1, 1);

		StandardEvaluationContext context = new StandardEvaluationContext();
//		context.setVariable("name", name);
		context.setVariable("gender", gender);
		context.setVariable("birthday", birthday);
		
		context.setVariable("other", "otherValue"); // 添加一个额外的变量以测试上下文

		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression(
			"#name != null && #name != '' && #gender != null && #gender != '' && #birthday != null"
		).getValue(context, Boolean.class);

		assertTrue(result);
	}

    @Test
    public void testPurchaseDate(){
        List<PurchaseEntity> list = new ArrayList<>();
        PurchaseEntity a = new PurchaseEntity();
        a.setPurchaseDate(LocalDateTime.now());
        list.add(a);
        PurchaseEntity b = new PurchaseEntity();
        b.setPurchaseDate(LocalDateTime.of(2025,1,1,0,0));
        list.add(b);

        StandardEvaluationContext context = new StandardEvaluationContext();
        context.setVariable("days", 3);
        context.setVariable("orders", list);

        ExpressionParser parser = new SpelExpressionParser();
        // 获取满足条件的订单列表
        List<PurchaseEntity> result = parser.parseExpression(
                "#orders.?[purchaseDate.isAfter(T(java.time.LocalDateTime).now().minusDays(#days))]"
        ).getValue(context, List.class);

//        assertNotNull(result);
//        assertEquals(2, result.size()); // 应该有两个订单满足条件
        System.out.println("Result size is " + result.size());
    }


}
