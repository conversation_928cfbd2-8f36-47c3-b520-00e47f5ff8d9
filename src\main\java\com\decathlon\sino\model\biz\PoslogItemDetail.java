package com.decathlon.sino.model.biz;

import com.fasterxml.jackson.databind.PropertyNamingStrategy.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonNaming(SnakeCaseStrategy.class)
public class PoslogItemDetail implements Serializable {

	private static final long serialVersionUID = 1L;

	private String unitaryAmount;
	private String totalPrice;
	private String totalPriceTaxEx;
	private String extModelCode;
	private String intModelCode;
	private String intArticleCode;
	private String extArticleCode;
	private Integer quantity;
	private String label;
	private String articleCode;
	private String universCode;
	private String itemId;
	private String itemRfId;
	private String size;
	private String color;
	private String pixlUrl;
	

}
