<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <contextName>${HOSTNAME}</contextName>
    <springProperty scope="context" name="appName" source="spring.application.name"/>
    <springProperty scope="context" name="logLevel" source="logging.level.customize"/>

    <springProfile name="local">
        <appender name="consoleAppender" class="ch.qos.logback.core.ConsoleAppender">
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>DEBUG</level>
            </filter>
            <layout class="ch.qos.logback.classic.PatternLayout">
                <pattern>
                    %d{yyyy-MM-dd HH:mm:ss.SSS} %-5p [%X{traceId}] [%thread] [%logger{50}.%M[%L]] - %msg %n
                </pattern>
            </layout>
        </appender>
        <root level="INFO">
            <appender-ref ref="consoleAppender"/>
        </root>
        <logger name="org.apache.kafka" level="WARN"/>
        <logger name="org.springframework.kafka" level="WARN"/>
    </springProfile>

    <springProfile name="dev">
        <appender name="consoleAppender" class="ch.qos.logback.core.ConsoleAppender">
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>DEBUG</level>
            </filter>
            <layout class="ch.qos.logback.classic.PatternLayout">
                <pattern>
                    %d{yyyy-MM-dd HH:mm:ss.SSS} %-5p [%X{traceId}] [%thread] [%logger{50}.%M[%L]] - %msg %n
                </pattern>
            </layout>
        </appender>

        <appender name="kafkaAppender" class="com.github.danielwegener.logback.kafka.KafkaAppender">
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp>
                        <timeZone>GMT+8</timeZone>
                    </timestamp>
                    <pattern>
                        <pattern>
                            {
                            "logger_name": "%logger{40}",
                            "thread_name": "%thread",
                            "level": "%level",
                            "instance_id": "%contextName",
                            "module_name": "${appName}",
                            "message": "%marker%m%ex{full} - %logger - %F:%L%n"
                            }
                        </pattern>
                    </pattern>
                </providers>
            </encoder>

            <topic>member_platform_identity_service_logs</topic>
            <keyingStrategy class="com.github.danielwegener.logback.kafka.keying.HostNameKeyingStrategy"/>
            <deliveryStrategy class="com.github.danielwegener.logback.kafka.delivery.AsynchronousDeliveryStrategy"/>
            <producerConfig>bootstrap.servers=de-kafka-cn-log.pp.dktapp.cloud:9092</producerConfig>
            <producerConfig>acks=0</producerConfig>
            <producerConfig>linger.ms=1000</producerConfig>
            <producerConfig>max.block.ms=0</producerConfig>
            <producerConfig>client.id=${HOSTNAME}-${CONTEXT_NAME}-logback</producerConfig>
        </appender>


        <root level="INFO">
            <appender-ref ref="consoleAppender"/>
            <appender-ref ref="kafkaAppender"/>
        </root>
        <logger name="com.decathlon.consents" level="${logLevel}"/>
        <logger name="org.apache.kafka" level="WARN"/>
        <logger name="org.springframework.kafka" level="WARN"/>
    </springProfile>

    <springProfile name="pprod">
        <appender name="consoleAppender" class="ch.qos.logback.core.ConsoleAppender">
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>DEBUG</level>
            </filter>
            <layout class="ch.qos.logback.classic.PatternLayout">
                <pattern>
                    %d{yyyy-MM-dd HH:mm:ss.SSS} %-5p [%X{traceId}] [%thread] [%logger{50}.%M[%L]] - %msg %n
                </pattern>
            </layout>
        </appender>

        <appender name="kafkaAppender" class="com.github.danielwegener.logback.kafka.KafkaAppender">
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp>
                        <timeZone>GMT+8</timeZone>
                    </timestamp>
                    <pattern>
                        <pattern>
                            {
                            "logger_name": "%logger{40}",
                            "thread_name": "%thread",
                            "level": "%level",
                            "instance_id": "%contextName",
                            "module_name": "${appName}",
                            "message": "%marker%m%ex{full} - %logger - %F:%L%n"
                            }
                        </pattern>
                    </pattern>
                </providers>
            </encoder>

            <topic>member_platform_identity_service_logs</topic>
            <keyingStrategy class="com.github.danielwegener.logback.kafka.keying.HostNameKeyingStrategy"/>
            <deliveryStrategy class="com.github.danielwegener.logback.kafka.delivery.AsynchronousDeliveryStrategy"/>
            <producerConfig>bootstrap.servers=de-kafka-cn-log.pp.dktapp.cloud:9092</producerConfig>
            <producerConfig>acks=0</producerConfig>
            <producerConfig>linger.ms=1000</producerConfig>
            <producerConfig>max.block.ms=0</producerConfig>
            <producerConfig>client.id=${HOSTNAME}-${CONTEXT_NAME}-logback</producerConfig>
        </appender>


        <root level="INFO">
            <appender-ref ref="consoleAppender"/>
            <appender-ref ref="kafkaAppender"/>
        </root>
        <logger name="com.decathlon.consents" level="${logLevel}"/>
        <logger name="org.apache.kafka" level="WARN"/>
        <logger name="org.springframework.kafka" level="WARN"/>
    </springProfile>

    <springProfile name="prod">
        <appender name="consoleAppender" class="ch.qos.logback.core.ConsoleAppender">
            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
                <level>INFO</level>
            </filter>
            <layout class="ch.qos.logback.classic.PatternLayout">
                <pattern>
                    %d{yyyy-MM-dd HH:mm:ss.SSS} %-5p [%X{traceId}] [%thread] [%logger{50}.%M[%L]] - %msg %n
                </pattern>
            </layout>
        </appender>

        <appender name="kafkaAppender" class="com.github.danielwegener.logback.kafka.KafkaAppender">
            <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
                <providers>
                    <timestamp>
                        <timeZone>GMT+8</timeZone>
                    </timestamp>
                    <pattern>
                        <pattern>
                            {
                            "logger_name": "%logger{40}",
                            "thread_name": "%thread",
                            "level": "%level",
                            "instance_id": "%contextName",
                            "module_name": "${appName}",
                            "message": "%marker%m%ex{full} - %logger - %F:%L%n"
                            }
                        </pattern>
                    </pattern>
                </providers>
            </encoder>

            <topic>memeber_platform_identity_service_logs</topic>
            <keyingStrategy class="com.github.danielwegener.logback.kafka.keying.HostNameKeyingStrategy"/>
            <deliveryStrategy class="com.github.danielwegener.logback.kafka.delivery.AsynchronousDeliveryStrategy"/>
            <producerConfig>bootstrap.servers=alikafka-post-cn-x0r2zaew5006-1-vpc.alikafka.aliyuncs.com:9092,alikafka-post-cn-x0r2zaew5006-2-vpc.alikafka.aliyuncs.com:9092,alikafka-post-cn-x0r2zaew5006-3-vpc.alikafka.aliyuncs.com:9092</producerConfig>
            <producerConfig>acks=0</producerConfig>
            <producerConfig>linger.ms=1000</producerConfig>
            <producerConfig>max.block.ms=0</producerConfig>
            <producerConfig>client.id=${HOSTNAME}-${CONTEXT_NAME}-logback</producerConfig>
        </appender>


        <root level="INFO">
            <appender-ref ref="kafkaAppender"/>
            <appender-ref ref="consoleAppender"/>
        </root>
        <logger name="com.decathlon.consents" level="${logLevel}"/>
        <logger name="org.apache.kafka" level="WARN"/>
        <logger name="org.springframework.kafka" level="WARN"/>
    </springProfile>
</configuration>