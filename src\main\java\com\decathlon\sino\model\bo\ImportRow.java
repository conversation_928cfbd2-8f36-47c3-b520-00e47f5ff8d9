
package com.decathlon.sino.model.bo;

import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;

@Data
@JsonNaming(SnakeCaseStrategy.class)
public class ImportRow {

    @ExcelProperty("personId")
	private String personId;
    @ExcelProperty("卡号")
	private String cardNumber;
    @ExcelProperty("手机号")
	private String mobile;
    @ExcelProperty("原因")
	private String reason;
//    @ExcelProperty("生效时间")
//    private Date effectiveStartTime;
//    @ExcelProperty("实效时间")
//    private Date effectiveEndTime;
    
    private Date createdTime; // 创建时间
    private String operator; // 操作人
    
    private Integer rowIndex; // 行索引，用于标识行号
}
