package com.decathlon.sino.component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.springframework.stereotype.Component;

import com.decathlon.sino.data.dao.RcBlackListRuleEntityRepository;
import com.decathlon.sino.data.dao.RcBlackListRuleParamEntityRepository;
import com.decathlon.sino.data.entity.RcBlackListRuleEntity;
import com.decathlon.sino.data.entity.RcBlackListRuleParamEntity;
import com.decathlon.sino.model.bo.ParamDef;
import com.decathlon.sino.model.bo.RuleDefinition;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * RuleCacheComponent is responsible for loading and caching rule definitions
 * from the database. It initializes the cache on startup and provides a method
 * to reload all rules.
 */
@Component
@AllArgsConstructor
@Data
public class RuleCacheComponent {

	private final RcBlackListRuleEntityRepository rcBlackListRuleEntityRepository;
	private final RcBlackListRuleParamEntityRepository rcBlackListRuleParamEntityRepository;

	private Map<String, RuleDefinition> rules = new HashMap<>();

	@PostConstruct
	public void init() {
		reloadAll();
	}

	public synchronized void reloadAll() {
		// get all enabled rules
		List<RcBlackListRuleEntity> ruleRows = rcBlackListRuleEntityRepository.findEnabled();
		
		// get all params for the rules
		List<RcBlackListRuleParamEntity> paramRows =  rcBlackListRuleParamEntityRepository
				.findByRuleIds(ruleRows.stream().map(RcBlackListRuleEntity::getId).toList());
		
		// group params by ruleId
		Map<Long, List<RcBlackListRuleParamEntity>> paramMap = paramRows.stream()
				.collect(Collectors.groupingBy(RcBlackListRuleParamEntity::getRuleId));

		// create a map of rule code to RuleDefinition
		Map<String, RuleDefinition> tmp = new HashMap<>();
		for (RcBlackListRuleEntity r : ruleRows) {
			List<ParamDef> params = new ArrayList<>();
			for (RcBlackListRuleParamEntity p : paramMap.getOrDefault(r.getId(), Collections.emptyList())) {
				Object dv = parseDefault(p.getDefaultValue(), p.getParamType());
				params.add(new ParamDef(p.getParamName(), dv,p.getEnableConfig()));
			}
		    RuleDefinition def = new RuleDefinition(
		            r.getRuleCode(),
		            r.getExpression(),
		            r.getThreshold(),
		            r.getWeight(),
		            params);
		    tmp.put(r.getRuleCode(), def);
		}
		
		this.rules = tmp;
	}

	private Object parseDefault(String value, String type) {
		    switch(type) {
		      case "number":  return new BigDecimal(value);
		      case "boolean": return Boolean.parseBoolean(value);
		      case "LIST": {
					ObjectMapper mapper = new ObjectMapper();
					List<String> list;
					try {
						list = mapper.readValue(value, new TypeReference<List<String>>() {
						});
						return list;
					} catch (JsonMappingException e) {
						e.printStackTrace();
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
				}
		      default:        return value;
		    }
		  }

	/**
	 * Get all rules by business type and object type
	 * @param bizType
	 * @param objectType
	 * @return
	 */
	public List<RuleDefinition> getAllByBizAndObj(String bizType, String objectType) {
		List<RuleDefinition> reuslts = new ArrayList<>();
		// get all enabled rules
		List<RcBlackListRuleEntity> ruleRows = rcBlackListRuleEntityRepository.findByObjectTypeAndBizType(objectType, bizType);
		
		// get all params for the rules
		List<RcBlackListRuleParamEntity> paramRows =  rcBlackListRuleParamEntityRepository
				.findByRuleIds(ruleRows.stream().map(RcBlackListRuleEntity::getId).toList());
		
		// group params by ruleId
		Map<Long, List<RcBlackListRuleParamEntity>> paramMap = paramRows.stream()
				.collect(Collectors.groupingBy(RcBlackListRuleParamEntity::getRuleId));

		// create a map of rule code to RuleDefinition
		for (RcBlackListRuleEntity r : ruleRows) {
			List<ParamDef> params = new ArrayList<>();
			for (RcBlackListRuleParamEntity p : paramMap.getOrDefault(r.getId(), Collections.emptyList())) {
				Object dv = parseDefault(p.getDefaultValue(), p.getParamType());
				params.add(new ParamDef(p.getParamName(), dv,p.getEnableConfig()));
			}
		    RuleDefinition def = new RuleDefinition(
		            r.getRuleCode(),
		            r.getExpression(),
		            r.getThreshold(),
		            r.getWeight(),
		            params);
		    reuslts.add(def);
		}
		
		return reuslts;
		
	
	}

}
