package com.decathlon.sino.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;


@Component
public class ServiceEnableConfig {

    @Value("${oneId.api.identity.exist:true}")
    private Boolean oneIdServiceExist;
    @Value("${identity.self.enable:false}")
    private Boolean selfEnable;

    public Boolean getOneIdServiceExist() {
        return oneIdServiceExist;
    }

    public Boolean getSelfEnable() {
        return selfEnable;
    }


}
