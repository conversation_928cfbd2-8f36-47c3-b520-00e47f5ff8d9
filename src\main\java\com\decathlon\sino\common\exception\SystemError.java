package com.decathlon.sino.common.exception;

import lombok.Getter;

@Getter
public enum SystemError implements BaseError {
    ACCESS_DENIED("System_2", "Access Denied."),
    PARAMETER_ERROR("System_1", "parameter error."),
    POSLOG_DATA_ERROR("System_3", "Poslog data error."),
    NOT_MEMBER_ERROR("System_4", "Not member error.");

    private final String code;

    private final String message;

    SystemError(String code, String message) {
        this.code = code;
        this.message = message;
    }

}
