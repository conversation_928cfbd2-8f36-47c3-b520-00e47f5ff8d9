package com.decathlon.sino.common.annotations;

import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

@Slf4j
public class PhoneValidator implements ConstraintValidator<Phone, String> {

    public boolean isValid(String s, ConstraintValidatorContext context) {
        boolean result = false;
        try {
            result = Pattern.matches("1[3456789]\\d{9}$", s);
        } catch (Exception e) {
            log.error("验证手机号格式时发生异常，异常信息：", e);
        }
        return result;
    }

}
