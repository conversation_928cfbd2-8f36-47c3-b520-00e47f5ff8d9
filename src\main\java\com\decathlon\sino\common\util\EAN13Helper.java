package com.decathlon.sino.common.util;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Methodes utilitaires concernant les numeros de carte EAN13.
 * <AUTHOR>
 */
public final class EAN13Helper {


    /**
     * Indique si le numero passe en parametre est EAN13.
     */
    public static boolean isEAN13(final String numeroCarte) {
        if (!StringUtils.isNumeric(numeroCarte) || numeroCarte.length() != 13) {
            return false;
        }

        // cn card number must start with "209"
        if (!numeroCarte.startsWith("209")) {
            return false;
        }

        return (calculateControlKey(numeroCarte) == Integer.parseInt(numeroCarte.substring(12, 13)));
    }

    /**
     * Transform a card number to a teammate card number(2098XXXXXXXX)
     * @param cardNumber from the PoolCard (2090XXXXXXXX or 2091XXXXXXXX)
     * @return teammate card number
     */
    public static String transformCardNumberToTeammateCardNumber(String cardNumber) {

        if (StringUtils.isNumeric(cardNumber) && StringUtils.isNotBlank(cardNumber) && cardNumber.length() == 13) {
            //remove key
            cardNumber = cardNumber.substring(0, cardNumber.length() - 1);
            //determinate n
            int coefficient = 8 - Integer.parseInt(cardNumber.substring(3, 4));
            //add n*10exp8
            cardNumber = String.valueOf(Long.parseLong(cardNumber) + (int) (coefficient * Math.pow(10, 8)));
            //get card number EAN13
            cardNumber = cardNumber + calculateControlKey(cardNumber);
        }
        return cardNumber;
    }

    protected static int calculateControlKey(final String cardNumberWithoutKey) {
        final int[] cardNumber = new int[cardNumberWithoutKey.length()];
        for (int i = 0; i < cardNumberWithoutKey.length(); i++) {
            cardNumber[i] = Integer.valueOf(cardNumberWithoutKey.substring(i, i + 1));
        }

        final int sommePair = 3 * (cardNumber[1] + cardNumber[3] + cardNumber[5] + cardNumber[7] + cardNumber[9] + cardNumber[11]);
        final int sommeImpair = cardNumber[0] + cardNumber[2] + cardNumber[4] + cardNumber[6] + cardNumber[8] + cardNumber[10];
        final int modulo10 = (sommePair + sommeImpair) % 10;
        if (modulo10 == 0) {
            return 0;
        } else {
            return (10 - modulo10);
        }
    }

    public String getNextEAN13(final String last) {
        long l = Long.parseLong(last);

        short gardeFou = 0;
        while (true) {
            final String numeroCarte = String.valueOf(++l);
            if (EAN13Helper.isEAN13(numeroCarte)) {
                return numeroCarte;
            }
            // sait-on jamais, mieux vaut un nullpointer au dessus qu'un stackoverflow...
            if (++gardeFou > 10000) {
                return null;
            }
        }
    }

    public List<String> genererListeEAN13(final String from, final int nombre) {
        final List<String> res = new ArrayList<>();
        String lastGenerated = from;
        for (int i = 0; i < nombre; i++) {
            lastGenerated = getNextEAN13(lastGenerated);
            res.add(lastGenerated);
        }
        return res;
    }

}

