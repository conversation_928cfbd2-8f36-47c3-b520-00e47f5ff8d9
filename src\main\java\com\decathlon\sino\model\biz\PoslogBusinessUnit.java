package com.decathlon.sino.model.biz;

import com.fasterxml.jackson.databind.PropertyNamingStrategy.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonNaming(SnakeCaseStrategy.class)
public class PoslogBusinessUnit implements Serializable{
	
	private static final long serialVersionUID = 1L;
	
	private String thirdType;
	private String thirdNumber;
	private String subThirdNumber;
	private String storeName;
	private String countryCode;

}
