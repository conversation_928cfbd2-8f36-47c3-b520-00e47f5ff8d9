package com.decathlon.sino.kafka.config;

import io.confluent.kafka.serializers.KafkaAvroDeserializer;
import io.confluent.kafka.serializers.KafkaAvroDeserializerConfig;
import lombok.AllArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.security.scram.ScramLoginModule;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.stereotype.Component;

import com.decathlon.sino.data.entity.KafkaListenerStrategyConfigEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@AllArgsConstructor
public class KafkaContainerFactoryProvider {


    private final KafkaListenerService kafkaListenerStrategyService;

    private final Map<String, ConcurrentKafkaListenerContainerFactory<String, Object>> factoryCache =
            new ConcurrentHashMap<>();

    public ConcurrentKafkaListenerContainerFactory<String, Object> getContainerFactory(String serverName) {
        return factoryCache.computeIfAbsent(serverName, this::createContainerFactory);
    }

    public List<ConcurrentKafkaListenerContainerFactory<String, Object>> getAllContainerFactories() {
        List<String> activeServerNames = kafkaListenerStrategyService.getAllStrategyConfigs().stream()
                .filter(KafkaListenerStrategyConfigEntity::getIsActive)
                .map(KafkaListenerStrategyConfigEntity::getServerName)
                .distinct()
                .toList();

        return activeServerNames.stream()
                .map(this::getContainerFactory)
                .toList();
    }

    private ConcurrentKafkaListenerContainerFactory<String, Object> createContainerFactory(String serverName) {
        KafkaListenerStrategyConfigEntity config = kafkaListenerStrategyService.getKafkaConfigByServer(serverName);
        if (config == null) {
            throw new IllegalStateException("No configuration found for server: " + serverName);
        }

        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, config.getBootstrapServers());
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, getEnableAutoCommit(config));
        props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, getAutoCommitInterval(config));

        props.put(ConsumerConfig.GROUP_ID_CONFIG, config.getGroupId());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());

        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, getAutoOffsetReset(config));
        props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, "1");
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, config.getAutoOffsetReset());
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);

        if (config.getRetryBackoffMs() != null) {
            props.put(ConsumerConfig.RETRY_BACKOFF_MS_CONFIG, config.getRetryBackoffMs());
        }

        if (config.getPartitionAssignmentStrategyConfig() != null) {
            props.put(ConsumerConfig.PARTITION_ASSIGNMENT_STRATEGY_CONFIG, config.getPartitionAssignmentStrategyConfig());
        }

        if (config.getSpecificAvroReaderConfig() != null && config.getSpecificAvroReaderConfig()) {
            props.put(KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG, true);
            props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, config.getValueDeserializerClass());
            props.put(KafkaAvroDeserializerConfig.AUTO_REGISTER_SCHEMAS, config.getAutoRegisterSchemas());
            props.put(KafkaAvroDeserializerConfig.SCHEMA_REGISTRY_URL_CONFIG, config.getSchemaRegistry());
        }

        if (config.getSecurityProtocol() != null) {
            props.put("security.protocol", config.getSecurityProtocol());
        }

        if (config.getSaslMechanism() != null) {
            props.put("sasl.mechanism", config.getSaslMechanism());
        }

        if (config.getPassword() != null) {
            props.put("sasl.jaas.config", credentials(config.getUsername(), config.getPassword()));
        }

        ConsumerFactory<String, Object> consumerFactory = new DefaultKafkaConsumerFactory<>(props);

        ConcurrentKafkaListenerContainerFactory<String, Object> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);
        return factory;
    }

    public static String credentials(String username, String password) {
        return String.format("%s required username=\"%s\" password=\"%s\";",
                ScramLoginModule.class.getName(), username, password);
    }

    private String getAutoOffsetReset(KafkaListenerStrategyConfigEntity config) {
        return config.getAutoOffsetReset() == null ? "latest" : config.getAutoOffsetReset();

    }

    private Integer getAutoCommitInterval(KafkaListenerStrategyConfigEntity config) {
        return config.getAutoCommitInterval() == null ? 100 : config.getAutoCommitInterval();

    }

    private Boolean getEnableAutoCommit(KafkaListenerStrategyConfigEntity config) {
        return config.getEnableAutoCommit() == null ? Boolean.TRUE : config.getEnableAutoCommit();
    }

    public void refreshContainerFactories() {
        factoryCache.clear();
    }
} 