# 项目目录
Sino-Aud/
├── Dockerfile
├── Jenkinsfile
├── pom.xml 项目依赖
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/
│   │   │       └── decathlon/
│   │   │           └── sino/
│   │   │               ├── App.java 启动文件
│   │   │               ├── common/  一些配置的文件
│   │   │               │   ├── annotations/
│   │   │               │   ├── aop/
│   │   │               │   ├── component/
│   │   │               │   ├── config/
│   │   │               │   ├── constant/
│   │   │               │   ├── exception/
│   │   │               │   └── util/
│   │   │               ├── component/ 一个组件
│   │   │               │   └── excel/
│   │   │               ├── controller/ controller 层面代码
│   │   │               ├── data/   数据访问层
│   │   │               │   ├── dao/
│   │   │               │   └── entity/
│   │   │               ├── kafka/ kafka 相关的内容
│   │   │               │   ├── biz/  kafka 相关的数据处理
│   │   │               │   └── config/ kafka 配置数据
│   │   │               ├── model/ 模型代码
│   │   │               │   ├── bo/ 
│   │   │               │   ├── criteria/
│   │   │               │   ├── input/ 输入的dto
│   │   │               │   └── ouput/ 输出的dto
│   │   │               ├── service/  服务层代码
│   │   │               │   ├── biz/  业务代码
│   │   │               │   └── impl/ 共用的服务层实现
│   │   │               └── task/  任务相关的代码
│   │   └── resources/
│   │       ├── application-unit_test.yml
│   │       ├── application.yml
│   │       ├── logback-spring.xml
│   │       └── META-INF/
│   └── test/
│       └── java/
│           └── com/
│               └── decathlon/
│                   └── sino/
│                       ├── common/
│                       │   └── util/
│                       │       ├── ExpressionUtilTest.java
│                       │       └── MemberExpressionUtilTest.java
│                       └── service/
│                           └── impl/
│                               └── PointAuditServiceImplTest.java
└── target/

# 需要做测试规则

 全员的签名门店
- Usual store [in] [MP&官网、Tmall、JD、抖音、PDD、线下门店、美团、JDDJ]
- 近期注册用户
- 注册时间 [小于] [30] 天

 订单日期 [小于等于] [3] 天
- 订单渠道 [in] [MP&官网、Tmall、JD、抖音、PDD、线下门店、美团、JDDJ]
- 订单金额 [大于] [5000] 的订单

 单个商品行 (且)
- 购买数量 [大于] [10]
- 商品行总金额 [大于] [2000]

 订单的收费地址可信度低 (仅针对MPM、官网渠道) - 且
- 详细地址字段 [小于] [8]
- 详细地址中文字数 [小于] [1]

 历史负积分用户
- 历史积分余额 (固定选项) [小于] [-10000] - 现有负积分用户+功能上线后开始记录

 规则 (可能配置到场景)
- 规则条件

 长期注册用户
- 注册时间 [大于] [365] 天

 高价值用户 (后续基础分值)
- 90天前有订单

 订单数 [大于] [10]
- 净订单金额 [大于] [3000]

 姓名 [非空]
- 性别 [非空]
- 生日 [非空]

 个人信息完整程度高 (且)
- 线下活动参与多 (目前数据未入aid)

 运动偏好 [非空]
- 运动连 活动参与次数 [大于] [10]
