server:
  servlet:
    context-path: /portal/api/audit
  port: 8282	
logging:
  config: classpath:logback-spring.xml
  level:
    ROOT: INFO
    com.decathlon.sino.controller.*: ${LOGGING_LEVEL:ERROR}
    customize: ${LOG_LEVEL:INFO}
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} %-5p [%-31t] [%-54logger{0}] [%M] [%m] %ex{full} - %logger  - %F:%L%n'
spring:
  kafka:
    bootstrap-servers:  ${KAFKA_HOST:alikafka-post-cn-7pp2tz53u00a-1-vpc.alikafka.aliyuncs.com:9092,alikafka-post-cn-7pp2tz53u00a-2-vpc.alikafka.aliyuncs.com:9092,alikafka-post-cn-7pp2tz53u00a-3-vpc.alikafka.aliyuncs.com:9092}  # Kafka集群的地址
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer  # 键的序列化器
      value-serializer: org.apache.kafka.common.serialization.StringSerializer  # 值的序列化器
      acks: all  # 确认所有副本都已收到消息
      retries: 3  # 发送失败时的重试次数
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  profiles:
    active: ${active_profile:local}
  jpa:
    database: POSTGRESQL
    show-sql: false
    hibernate.ddl-auto: update
    properties:
      hibernate:
        jdbc.lob.non_contextual_creation: true
        dialect:
          org:
            jsonb: CustomPostgreSqlDialect
  database:
    driverCLassName: org.postgresql.Driver
  datasource:
    url: jdbc:postgresql://${POSTGRES_URL:127.0.0.1}:${POSTGRES_PORT:5432}/${POSTGRES_DB:audit_si}?currentSchema=${CURRENT_SCHEMA:public}
    username: ${POSTGRES_USER:postgres}
    password: ${POSTGRES_PASSWORD:123456}
    type: com.zaxxer.hikari.HikariDataSource
  cache:
    type: redis
  redis:
    host: ${MEMBERSHIP_REDIS_HOST:127.0.0.1}
    port: ${MEMBERSHIP_REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: ${REDIS_TIME_OUT:30000}
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
oneId:
  api:
    identity:
      host: ${ONE_ID_HOST:https://crm-api-cn.pp.dktapp.cloud/mydkt-server-mvc/ajax/api/}
      exist: true
aop:
  log:
    enable: true
identity:
  scheduler:
    enabled: true
    syncOthers:
      enabled: ${SYNC_OTHERS_MODEL_ENABLE:true}
      cron: ${SYNC_OTHERS_MODEL_CORN:0 30 23 * * ?}
    addCardnumber:
      enabled: ${POOL_CARDNUMBER_ADD_ENABLE:false}
      cron: ${POOL_CARDNUMBER_ADD_CORN:0 0 2 * * ?}
    syncExecuteFragment:
      enabled: ${EXECUTE_FRAGMENT_ENABLE:false}
      cron: ${EXECUTE_FRAGMENT_CORN:0 0/1 10-23 * * ?}  
    syncCalculateFragment:
      enabled: ${CALCULATE_FRAGMENT_ENABLE:false}
      cron: ${CALCULATE_FRAGMENT_CORN:0/30 * 10-23 * * ?}  
    syncEventNotification:
      enabled: ${EVENT_NOTIFICATION:false}
      cron: ${EVENT_NOTIFICATION_CORN:0 0/1 * * * ?}  										  
member:
  platform:
    host: ${MEMBER_PLATFORM_HOST:http://member-platform-cn.pp.dktapp.cloud/identity/}
user:
  token:
    service:
      authorization: ${USER_SERVICE_AUTHORIZATION:OXJBdTY2WFFiYnNXb25QVWlnM1lBdkZoUGl3ZG9jdEM6SE5LQzM3TE1iNzN2VjZvSG9iOHRjU2thdFpHUzZRa1dnTGpRWUVQSHB0Q201RVFSb2l5YURKTFhkVTdKd3ZHQg==}
      url: ${USER_TOKEN_SERVICE_URL:https://api-cn-pp.dktapp.cloud/facade_identification/connection/api/v1/user_token}
      api:
        key: ${USER_TOKEN_SERVICE_API_KEY:925ea87f-90dc-46c8-8678-637990bf3519}	
fed:
  token:
    service:
      url: ${FED_TOKEN_SERVICE_URL:https://preprod.idpdecathlon.oxylane.com/as/token.oauth2?grant_type=client_credentials}
      authorization: ${FED_SERVICE_AUTHORIZATION:Basic QzI5ODA4OGVjOTg0MTVhYmQzNzkxZjdlOTUzMDRjOWU1OGE3ZjU0Y2E6UzhadlN2cldpRzluSE1RcFhGV3U4dmdtdTZiNWFQWk1uMWhBVmdtVld3QUFLUndtamdaWXhicEJEY2lWeW9IUQ==}

