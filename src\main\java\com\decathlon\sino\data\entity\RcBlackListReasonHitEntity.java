package com.decathlon.sino.data.entity;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.decathlon.sino.data.entity.basic.IdEntity;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import java.math.BigDecimal;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@SuperBuilder
@Table(name = "rc_black_list_reason_hit")
public class RcBlackListReasonHitEntity extends IdEntity {

	private static final long serialVersionUID = 1L;
	private Long blackListId;
	private String ruleCode;
	private String ruleName;
	private String ruleGroup;
	private String ruleVersion;
	private String decision;
	private String decisionType;
	private BigDecimal score;
	private BigDecimal threshold;
	private String variables;
	private String evidence;
	private Date createTime;

}
