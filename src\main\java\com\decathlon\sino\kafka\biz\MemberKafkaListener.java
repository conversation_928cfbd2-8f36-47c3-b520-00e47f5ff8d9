package com.decathlon.sino.kafka.biz;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.stereotype.Component;

import com.decathlon.sino.kafka.biz.helper.MemberListenerProcessHelper;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * MemberKafkaListener is a Kafka listener that processes messages related to member events.
 * It uses the MemberListenerProcessHelper to handle the business logic for processing these messages.
 */
@Slf4j
@Component
@AllArgsConstructor
public class MemberKafkaListener implements KafkaListenerStrategy {
	
	private final MemberListenerProcessHelper memberListenerProcessHelper;
	
	@Override
	public void handleMessage(ConsumerRecord<String, Object> records) {
        try {
            log.info("Processing message: {}", records.value());
            memberListenerProcessHelper.process(records);
        } catch (Exception e) {
            log.error("Error processing message: {}", e.getMessage(), e);
            throw e;
        }
	}

	@Override
	public String getBizName() {
		return "MEMBER";
	}

	@Override
	public String getDescription() {
		return "Message processing receving strategy for member-related events";
	}

}
