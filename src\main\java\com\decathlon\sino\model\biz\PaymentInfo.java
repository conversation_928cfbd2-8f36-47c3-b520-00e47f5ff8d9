package com.decathlon.sino.model.biz;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PaymentInfo implements Serializable {
	
	private static final long serialVersionUID = 1L;

	@JsonProperty(value = "payment_type")
	private String paymentType;
	
	@JsonProperty(value = "amount")
	private Double amount;

	private String description;
	

}
