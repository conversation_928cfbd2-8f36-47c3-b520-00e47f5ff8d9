package com.decathlon.sino.service.impl.helper;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.decathlon.sino.component.RuleCacheComponent;
import com.decathlon.sino.data.dao.RcBlackListRuleEntityRepository;
import com.decathlon.sino.data.dao.RcBlackListRuleParamEntityRepository;
import com.decathlon.sino.data.entity.RcBlackListRuleEntity;
import com.decathlon.sino.data.entity.RcBlackListRuleParamEntity;
import com.decathlon.sino.model.bo.ParamDef;
import com.decathlon.sino.model.bo.RuleDefinition;
import com.decathlon.sino.service.biz.PointDbService;

import cn.hutool.json.JSONObject;
import lombok.AllArgsConstructor;

@Component
@AllArgsConstructor
//TODO
public class PointRiskHelper extends RiskHelper{
	
	private final PointDbService pointDbService;
	private final RuleCacheComponent ruleCacheComponent;

	public static Map<String, Object> getContext(String bizType, String obejctId, String objectType, JSONObject eventData) {
		
		
		Map<String, Object> ctx = new HashMap<>();
		String cardNo = eventData.getStr("cardNo");
		LocalDateTime startTime = LocalDateTime.parse(eventData.getStr("startTime"));
		LocalDateTime endTime = LocalDateTime.parse(eventData.getStr("endTime"));
		Integer pointChange = eventData.getInt("pointChange");
		ctx.put("cardNo", cardNo);
		ctx.put("startTime", startTime);
		ctx.put("endTime", endTime);
		ctx.put("pointChange", pointChange);
		return ctx;  
	}

}
