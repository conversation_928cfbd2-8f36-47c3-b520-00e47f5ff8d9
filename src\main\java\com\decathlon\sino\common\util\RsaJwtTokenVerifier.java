package com.decathlon.sino.common.util;

import io.jsonwebtoken.*;

public class RsaJwtTokenVerifier {

    public static final String BEARER = "Bearer";

    @SuppressWarnings("rawtypes")
    public static Claims getFedClaims(String token) {
        try {
            Jwt<Header, Claims> trusted = Jwts.parser().parseClaimsJwt(token);
            return trusted.getBody();
        } catch (UnsupportedJwtException e) {
            Jwt<Header, Claims> untrusted = Jwts.parser()
                    .parseClaimsJwt(token.substring(0, token.lastIndexOf(".") + 1));
            return untrusted.getBody();
        }
    }

    public static String getPersonId(String token) {
        if (token.contains(RsaJwtTokenVerifier.BEARER)) {
            token = token.split("\\s+")[1];
        }
        Claims c = RsaJwtTokenVerifier.getFedClaims(token);
        return c.get("person_id").toString();
    }

    public static String getOpenId(String token) {
        if (token.contains(RsaJwtTokenVerifier.BEARER)) {
            token = token.split("\\s+")[1];
        }
        Claims c = RsaJwtTokenVerifier.getFedClaims(token);
        return c.get("social_id").toString();
    }

}