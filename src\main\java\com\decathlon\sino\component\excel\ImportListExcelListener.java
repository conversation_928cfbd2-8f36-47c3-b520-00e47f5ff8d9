package com.decathlon.sino.component.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.decathlon.sino.model.bo.ImportRow;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.util.StringUtils;


@Slf4j
public class ImportListExcelListener extends AnalysisEventListener<ImportRow> {
    
    private final List<ImportRow> dataList = new ArrayList<>();
    private final Map<String, String> errorMap = new ConcurrentHashMap<>();
    
    @Override
    public void invoke(ImportRow data, AnalysisContext context) {
        // 数据校验
        if (!validateData(data)) {
            errorMap.put(String.valueOf(context.readRowHolder().getRowIndex() + 1), "personId, cardNumber, personId all empty");
            return;
        }
        data.setRowIndex(context.readRowHolder().getRowIndex() + 1); 
        dataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("Excel解析完成，共解析{}条数据", dataList.size());
    }

    private boolean validateData(ImportRow data) {
    	return  StringUtils.hasText(data.getPersonId()) 
                || StringUtils.hasText(data.getCardNumber())
                || StringUtils.hasText(data.getMobile());

    }

    public List<ImportRow> getDataList() {
        return dataList;
    }

    public Map<String, String> getErrorMap() {
        return errorMap;
    }
} 