package com.decathlon.sino.common.util;

import com.google.common.base.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.DigestUtils;
import org.springframework.web.util.UriComponentsBuilder;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-09-16 11:17
 **/
@Component
@Slf4j
public class AES256Util {

    /**
     * 密钥, 256位32个字节
     */
    public static final String DEFAULT_SECRET_KEY = "uBdUx82vPHkDKb284d7NkjFoNcKWBuka";

    private static final String AES = "AES";

    /**
     * 初始向量IV, 初始向量IV的长度规定为128位16个字节, 初始向量的来源为随机生成.
     */
    private static final byte[] KEY_VI = "12D91CF579EF6983".getBytes();

    /**
     * 加密解密算法/加密模式/填充方式
     */
    private static final String CIPHER_ALGORITHM = "AES/CBC/PKCS5Padding";

    private static java.util.Base64.Encoder base64Encoder = java.util.Base64.getEncoder();
    private static java.util.Base64.Decoder base64Decoder = java.util.Base64.getDecoder();

    static {
        java.security.Security.setProperty("crypto.policy", "unlimited");
    }

    private static final String KEY = "&key=";

    @Value("${sha256.key:dkt_dcn}")
    private String signatureKey;
    private static final String TOP_FIELD_SIGN = "sign";

    public static String getSHA256StrJava(String str) {
        MessageDigest messageDigest;
        String encodeStr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(str.getBytes(StandardCharsets.UTF_8));
            encodeStr = byteToHex(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return encodeStr;
    }

    public static boolean validateSign(String str, String sign) {
        boolean pass = false;
        String localSign = AES256Util.getSHA256StrJava(str);
        log.info("localsign ={},sign ={}", localSign, sign);
        if (Objects.equal(localSign, sign)) {
            pass = true;
        }
        return pass;
    }


    private static String byteToHex(byte[] bytes) {
        if (bytes == null) {
            return null;
        }
        StringBuilder stringBuilder = new StringBuilder();
        String temp = null;
        for (int i = 0; i < bytes.length; i++) {
            temp = Integer.toHexString(bytes[i] & 0xff);
            if (temp.length() == 1) {
                stringBuilder.append("0");
            }
            stringBuilder.append(temp);
        }
        return stringBuilder.toString().toUpperCase();
    }

    public String getLocalSign(String str) {
        return AES256Util.getSHA256StrJava(str + KEY + signatureKey).toUpperCase();
    }

    public static String getLocal(String str) {
        return AES256Util.getSHA256StrJava(str).toUpperCase();
    }

    public static String getParamStrFromMap(Map<String, String> params) {
        StringBuilder sb = new StringBuilder();
        if (params != null && !params.isEmpty()) {
            String[] keys = params.keySet().toArray(new String[0]);
            Arrays.sort(keys);
            for (int i = 0; i < keys.length; i++) {
                String name = keys[i];
                if (!TOP_FIELD_SIGN.equals(name)) {
                    sb.append(name);
                    sb.append(params.get(name));
                }
            }
        }
        return sb.toString();
    }

    /**
     * AES加密
     */
    public static String encode(String key, String content) {
        try {
            javax.crypto.SecretKey secretKey = new javax.crypto.spec.SecretKeySpec(key.getBytes(), AES);
            javax.crypto.Cipher cipher = javax.crypto.Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(javax.crypto.Cipher.ENCRYPT_MODE, secretKey, new javax.crypto.spec.IvParameterSpec(KEY_VI));

            // 获取加密内容的字节数组(这里要设置为utf-8)不然内容中如果有中文和英文混合中文就会解密为乱码
            byte[] byteEncode = content.getBytes(java.nio.charset.StandardCharsets.UTF_8);

            // 根据密码器的初始化方式加密
            byte[] byteAES = cipher.doFinal(byteEncode);

            // 将加密后的数据转换为字符串
            return base64Encoder.encodeToString(byteAES);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * AES解密
     */
    public static String decode(String key, String content) {
        try {
            javax.crypto.SecretKey secretKey = new javax.crypto.spec.SecretKeySpec(key.getBytes(), AES);
            javax.crypto.Cipher cipher = javax.crypto.Cipher.getInstance(CIPHER_ALGORITHM);
            cipher.init(javax.crypto.Cipher.DECRYPT_MODE, secretKey, new javax.crypto.spec.IvParameterSpec(KEY_VI));

            // 将加密并编码后的内容解码成字节数组
            byte[] byteContent = base64Decoder.decode(content);
            // 解密
            byte[] byteDecode = cipher.doFinal(byteContent);
            return new String(byteDecode, java.nio.charset.StandardCharsets.UTF_8);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String decodeForMPM(String key, String content) {
        Assert.notNull(key, "Aes secret required");
        String secret = DigestUtils.md5DigestAsHex(key.getBytes()).substring(8, 24).toUpperCase();
        return decode(secret, content);
    }

    public static void main(String[] args) {
        System.out.println(UriComponentsBuilder.fromHttpUrl("https://".concat("api/v3/customer_data/identity"))
                .queryParam("id_person", "xx").queryParam("ppays", "CN").queryParam("client_id", "cube")
                .queryParam("id_country_code", "CN").build().toUriString());
    }

}