package com.decathlon.sino.common.exception;


import java.text.MessageFormat;

/**
 * The Interface BaseErrorIF.
 */
public interface BaseError {

    /**
     * Gets the code.
     * @return the code
     */
    String getCode();

    /**
     * Gets the message.
     * @return the message
     */
    String getMessage();

    /**
     * With params.
     * @param params the params
     * @return the string
     */
    default String withParams(Object... params) {
        return MessageFormat.format(getMessage(), params);
    }

}
