package com.decathlon.sino.kafka.biz.helper;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.decathlon.ibuy.Poslog;
import com.decathlon.sino.common.util.DateUtil;
import com.decathlon.sino.data.entity.biz.AccountPointEntity;
import com.decathlon.sino.data.entity.biz.PurchaseEntity;
import com.decathlon.sino.model.biz.ItemInfoBpn;
import com.decathlon.sino.service.biz.PurchaseDbService;
import com.decathlon.sino.service.impl.PurchaseAduitServiceImpl;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.nrf_arts.ixretail.v6_0_0.poslog.POSLogBase;
import org.nrf_arts.ixretail.v6_0_0.poslog.TransactionDomainSpecific;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.xml.bind.JAXBException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.decathlon.sino.kafka.biz.helper.PoslogAnalyzeHelper.getPoslogBase;

@Component
@Slf4j
@AllArgsConstructor
public class PoslogListenerProcessHelper {

	private final PurchaseAduitServiceImpl purchaseAduitServiceImpl;

    @Autowired
	private PurchaseDbService purchaseDbService;


	@Async
	public void process(ConsumerRecord<String, Object> records) throws JAXBException {
			log.info("Processing message: {}", records.value());
			Poslog poslog = (Poslog)records.value();
			String xmlContent = poslog.getPayload().getDataXml();
			POSLogBase poslogBase = getPoslogBase(xmlContent);

			// acquire transaction
			final TransactionDomainSpecific transactionDomainSpecific = poslogBase.getTransaction()
					.get(0);
			PurchaseEntity purchase = transform(transactionDomainSpecific);
			Boolean success = purchaseDbService.save(purchase);
		log.info("process save purchase successfully: {}", success);

		//触发审计
			purchaseAduitServiceImpl.auditRisk(null, null, null, null, null, null);
			

	}

	private PurchaseEntity transform(TransactionDomainSpecific orderSpecific) {
		//todo如果有原订单号，通过原订单号去找是否有重复的
		PurchaseEntity purchase = purchaseDbService.findByTransactionId(orderSpecific.getTransactionID());
        if (Objects.isNull(purchase)) {
			return newTransaction(orderSpecific);
		}
		return renewTransaction(purchase,orderSpecific);

	}

	private PurchaseEntity newTransaction(TransactionDomainSpecific orderSpecific) {
		PurchaseEntity purchase = new PurchaseEntity();
		purchase.setCardNo(PoslogAnalyzeHelper.analyzeCustomerId(orderSpecific));

		purchase.setTransactionId(orderSpecific.getTransactionID());
		purchase.setPurchaseDate(DateUtil.convert(PoslogAnalyzeHelper.analyzeTransactionDate(orderSpecific)));

		//digital / physical
//		purchase.setChannel(PoslogAnalyzeHelper.analyzeChannel(orderSpecific));
		purchase.setChannel(PoslogAnalyzeHelper.analyzeSpecificChanel(orderSpecific));
		//在初始化订单的时候就有买的
		List<ItemInfoBpn> salePartItems =  PoslogAnalyzeHelper.analyzeItemInfos(orderSpecific, true, false);
		purchase.setSaleDetail(JSONUtil.toJsonStr(salePartItems));

		renewTransaction(purchase,orderSpecific);

		//todo 解析地址
		log.info("has save purchase {}",JSONUtil.toList(purchase.getReturnDetail(), ItemInfoBpn.class));
		return purchase;
	}
	private PurchaseEntity renewTransaction(PurchaseEntity purchase,TransactionDomainSpecific orderSpecific){
		//todo 线下如果分多笔退呢，所以要不就不合并了，还是分开存
//		 origin_transaction = transaction id？
		List<ItemInfoBpn> returnPartItems =  PoslogAnalyzeHelper.analyzeItemInfos(orderSpecific, false, true);
		purchase.setReturnDetail(JSONUtil.toJsonStr(returnPartItems));
        purchase.setOrigintransactionId(PoslogAnalyzeHelper.originTransactionIds(returnPartItems));
		purchase.setTotalPrice(PoslogAnalyzeHelper.analyzeTotalPrice(orderSpecific));
		purchase.setSalePrice(purchaseDbService.getSalePrice(orderSpecific));
		purchase.setReturnPrice(purchaseDbService.getReturnPrice(orderSpecific));
		purchase.setStatus(PoslogAnalyzeHelper.anaylyzeTransactionStatus(orderSpecific));
		purchase.setTransactionDate(PoslogAnalyzeHelper.analyzeTxMessageDate(orderSpecific));
		purchase.setUpdatedAt(new Date());
		return purchase;
	}

	public Map<String, Object> getPurchaseAuditMessage(String cardNo) {
		// TODO Auto-generated method stub
		return null;
	}


}
