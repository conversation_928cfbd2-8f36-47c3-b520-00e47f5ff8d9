package com.decathlon.sino.controller;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import com.decathlon.sino.common.annotations.StartSwaggerScan;

/**
 * <AUTHOR>
 */

@StartSwaggerScan
@RestController
@Slf4j
public class HealthCheckController {

    @GetMapping(value = "/ping")
    @ApiOperation(value = "check network")
    public String ping() {
        log.info("ping");
        return "ping";
    }

}
