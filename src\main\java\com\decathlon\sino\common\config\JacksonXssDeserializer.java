package com.decathlon.sino.common.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;

public class JacksonXssDeserializer extends StdDeserializer<String> {

    private static final long serialVersionUID = 6517465663078103528L;

    public JacksonXssDeserializer() {
        super(String.class);
    }

    @Override
    public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();
        return StringUtils.isBlank(value) ? value : JsoupUtil.clean(value);
    }
}
