package com.decathlon.sino.data.entity;

import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import com.decathlon.sino.data.entity.basic.IdEntity;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Table(name = "kafka_listener_strategy_config")
public class KafkaListenerStrategyConfigEntity extends IdEntity {

	private static final long serialVersionUID = 1L;

	@Column(name = "cluster_name", nullable = false)
	private String clusterName;

	@Column(name = "topic", nullable = false)
	private String topic;

	//具体的抓取策略名称
	@Column(name = "strategy_name", nullable = false)
	private String strategyName;
	
	private String serverName;

	@Column(name = "is_active")
	private Boolean isActive = true;

	@Column(name = "retry_count")
	private Integer retryCount = 3;

	@Column(name = "retry_interval_ms")
	private Integer retryIntervalMs = 1000;

	@Column(name = "error_topic")
	private String errorTopic;

	@Column(name = "dead_letter_topic")
	private String deadLetterTopic;
	
	@CreationTimestamp
	@Column(name = "created_at", updatable = false)
	private LocalDateTime createdAt;

	@UpdateTimestamp
	@Column(name = "updated_at")
	private LocalDateTime updatedAt;
	
	private String bootstrapServers;

	private String groupId;

	private String autoOffsetReset;
	
	private String topicName;

	private Boolean enableAutoCommit;

	private Integer autoCommitInterval;

	private Boolean specificAvroReaderConfig;

	private String valueDeserializerClass;

	private String retryBackoffMs;

	private String schemaRegistry;

	private String partitionAssignmentStrategyConfig;
	private String securityProtocol;
	private String saslMechanism;
	private String username;
	private String password;

	private Boolean AutoRegisterSchemas;

}
