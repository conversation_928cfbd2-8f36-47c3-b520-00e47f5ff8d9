package com.decathlon.sino.common.exception;

public class ServiceException extends BaseException {

    private static final long serialVersionUID = 4058317721550486338L;

    private static final String GENERIC_SERVICE_ERROR = "Internal Service Exception";

    public ServiceException(String code) {
        super(code, GENERIC_SERVICE_ERROR);
    }

    public ServiceException(String code, Object data) {
        super(code, GENERIC_SERVICE_ERROR, data);
    }

    public ServiceException(String code, String message) {
        super(code, message);
    }

    public ServiceException(String code, String message, Object data) {
        super(code, message, data);
    }

    public ServiceException(String code, String message, Throwable e) {
        super(code, message, e);
    }

    public ServiceException(BaseError error, Object... params) {
        super(error, params);
    }

    public ServiceException(String code, String message, Throwable e, Object data) {
        super(code, message, e, data);
    }

    public ServiceException(Object data, BaseError error, Object... params) {
        super(data, error, params);
    }
}
