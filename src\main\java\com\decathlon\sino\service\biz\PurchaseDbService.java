package com.decathlon.sino.service.biz;

import com.decathlon.sino.data.entity.biz.AccountPointEntity;
import com.decathlon.sino.data.entity.biz.PurchaseEntity;
import org.nrf_arts.ixretail.v6_0_0.poslog.TransactionDomainSpecific;

import java.math.BigDecimal;

public interface PurchaseDbService {
    Boolean save(PurchaseEntity purchaseEntity);

    Double getSalePrice(TransactionDomainSpecific orderSpecific);

    Double getReturnPrice(TransactionDomainSpecific orderSpecific);

    PurchaseEntity findByTransactionId(String transactionId);
}
