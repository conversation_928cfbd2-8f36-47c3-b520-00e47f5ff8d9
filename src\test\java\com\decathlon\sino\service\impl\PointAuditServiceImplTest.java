package com.decathlon.sino.service.impl;

import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.expression.Expression;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.decathlon.sino.App;

import lombok.extern.slf4j.Slf4j;

import com.decathlon.sino.data.dao.biz.AccountPointEntityRepository;
import com.decathlon.sino.data.entity.biz.AccountPointEntity;

import org.springframework.expression.spel.SpelParserConfiguration;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

@RunWith(SpringJUnit4ClassRunner.class)
@EnableAutoConfiguration
@SpringBootTest(classes = App.class)
@Slf4j
class PointAuditServiceImplTest {
	
	@Autowired
	private AccountPointEntityRepository accountPointEntityRepository;
	
    public static Integer sum(List<Integer> list) {
		Integer sum = 0;
		for (Integer i : list) {
			sum += i;
		}
		return sum;
	}
	
	@Test
	void test_produce_risk() throws NoSuchMethodException, SecurityException {
		
    	StandardEvaluationContext context = new StandardEvaluationContext();
    	context.registerFunction("sum", PointAuditServiceImplTest.class.getDeclaredMethod("sum", List.class));
		
		List<AccountPointEntity> accountPointList = accountPointEntityRepository.findAllByCardNo("*************");
		context.setVariable("accountPointList", accountPointList);  
		// 测试表达式是否正确
		SpelParserConfiguration config = new SpelParserConfiguration();
		SpelExpressionParser parser = new SpelExpressionParser(config);
		
		Expression expr2 = parser.parseExpression(
			    "#accountPointList.![pointChange]"
			);
		
		List<Integer> data1 = expr2.getValue(context, List.class);
		System.out.println("success");

		// Now try parsing your expression
		Expression expr = parser.parseExpression(
		    "#sum(#accountPointList.![pointChange]) > 300 "
		);
		
		Expression expr1 = parser.parseExpression(
			    "#sum(#accountPointList.![pointChange])"
			);
		
		Integer data =  expr1.getValue(context, Integer.class);
		System.out.println(data);
		
		expr.getValue(context, Boolean.class);
	}
}
