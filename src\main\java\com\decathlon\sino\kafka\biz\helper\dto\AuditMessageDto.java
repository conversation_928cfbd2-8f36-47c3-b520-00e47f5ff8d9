package com.decathlon.sino.kafka.biz.helper.dto;

import java.util.Date;

import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@JsonNaming(SnakeCaseStrategy.class)
public class AuditMessageDto {
	
	// Account information
	private Long personId;
	private String cardNumber;
	private String mobile;
	private String name;
	private String gender;
	private Date deleteDate;
	private Date registerDate;
	private String deleteReason;
	private Date birthDate;

	// Address information
	private Integer addressNumber;
	
	// Store information
	private String registerStoreNumber;
	private String registerStoreName;
	private String registerClientId;
	private String usualStoreNumber;
	private String usualStoreName;
	
	//event type  INFO, ADDRESS, STORE
	private String type;



}
