package com.decathlon.sino.data.dao;

import java.util.List;

import com.decathlon.sino.data.dao.basic.QueryDslBaseDao;
import com.decathlon.sino.data.entity.KafkaListenerStrategyConfigEntity;

public interface KafkaListenerStrategyConfigEntityRepository extends QueryDslBaseDao<KafkaListenerStrategyConfigEntity> {

	KafkaListenerStrategyConfigEntity findByClusterNameAndTopicAndIsActiveTrue(String clusterName, String topic);

	KafkaListenerStrategyConfigEntity findByServerNameAndIsActiveTrue(String serverName);

	List<KafkaListenerStrategyConfigEntity> findAllByServerNameAndIsActiveTrue(String serverName);

	List<KafkaListenerStrategyConfigEntity> findAllByTopicAndIsActiveTrue(String topic);
}
