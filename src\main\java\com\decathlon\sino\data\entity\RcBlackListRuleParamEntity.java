package com.decathlon.sino.data.entity;

import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.Table;
import com.decathlon.sino.data.entity.basic.IdEntity;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@SuperBuilder
@Table(name = "rc_black_list_rule_param")
public class RcBlackListRuleParamEntity extends IdEntity {

	private static final long serialVersionUID = 1L;

	private Long ruleId;
	private String paramName;
	private String paramType;
	private String defaultValue;
	private String description;
	private Date createTime;
	private Date updateTime;
	private Boolean enableConfig;
}
