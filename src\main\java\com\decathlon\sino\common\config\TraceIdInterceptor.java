package com.decathlon.sino.common.config;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

import com.decathlon.sino.common.util.TraceIdUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * trace id 拦截器
 */
@Slf4j
public class TraceIdInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) throws Exception {
        String traceId = request.getHeader(TraceIdUtils.HEADER_TRACE_ID);
        TraceIdUtils.setTraceId(traceId);
        response.setHeader(TraceIdUtils.HEADER_TRACE_ID, TraceIdUtils.getTraceId());
        return HandlerInterceptor.super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler, Exception ex) throws Exception {
        TraceIdUtils.remove();
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }
}
