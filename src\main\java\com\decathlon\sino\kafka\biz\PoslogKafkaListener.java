package com.decathlon.sino.kafka.biz;

import com.decathlon.ibuy.Poslog;
import com.decathlon.sino.kafka.biz.helper.PointListenerProcessHelper;
import com.decathlon.sino.kafka.biz.helper.PoslogListenerProcessHelper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.xml.bind.JAXBException;

@Slf4j
@Component
@AllArgsConstructor
public class PoslogKafkaListener implements KafkaListenerStrategy {
	
	private final PoslogListenerProcessHelper poslogListenerProcessHelper;
	
//    @Override
//    public void handleMessage(ConsumerRecord<String, String> records, Acknowledgment ack) {
//        try {
//            log.info("Processing message: {}", records.value());
//            poslogListenerProcessHelper.process(records);
//            ack.acknowledge();
//        } catch (Exception e) {
//            log.error("Error processing message: {}", e.getMessage(), e);
//            throw e;
//        }
//    }

//    @Override
    public void handleMessage(ConsumerRecord<String, Object> records, Acknowledgment ack) throws JAXBException {
        try {
            log.info("Processing message: {}", records.value());
            poslogListenerProcessHelper.process(records);
            ack.acknowledge();
        } catch (Exception e) {
            log.error("Error processing message: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void handleMessage(ConsumerRecord<String, Object> records) throws JAXBException {
        try {
            log.info("Processing message: {}", records.value());
            poslogListenerProcessHelper.process(records);
//            ack.acknowledge();
        } catch (Exception e) {
            log.error("Error processing message: {}", e.getMessage(), e);
            throw e;
        }
    }



    @Override
    public String getBizName() {
        return "ORDER";
    }

    @Override
    public String getDescription() {
        return "Message processing strategy with retry mechanism";
    }
} 