package com.decathlon.sino.data.entity.biz;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;


import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Table(name = "biz_account_info")
public class AccountInfoEntity {

	@Id
	@GeneratedValue(generator = "assigned_id", strategy = GenerationType.IDENTITY)
	private Long personId;
	private String cardNumber;
	private String mobile;
	private String name;
	private String gender;
	private Date deleteDate;
	private Date registerDate;
	private String deleteReason;
    private Date birthDate;
	
	// Address information
	private Integer addressNumber;
	
	// Store information
	private String registerStoreNumber;
	private String registerStoreName;
	private String registerClientId;
	private String usualStoreNumber;
	private String usualStoreName;
}
