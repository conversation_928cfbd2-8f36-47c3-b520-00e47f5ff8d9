package com.decathlon.sino.model.biz;

import com.fasterxml.jackson.databind.PropertyNamingStrategy.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonNaming(SnakeCaseStrategy.class)
public class PoslogPaymentDetail implements Serializable {

	private static final long serialVersionUID = -8326569810435807885L;
	private String paymentMode;
	private String paymentCode;
	private String currency;
	private String amount;
	private String paymentDate;

}
