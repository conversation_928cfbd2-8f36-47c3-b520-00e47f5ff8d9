package com.decathlon.sino.data.entity.basic;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;

@Data
@MappedSuperclass
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class IdEntity implements Serializable {

    private static final long serialVersionUID = 2955886978738326171L;

    @Id
    @GenericGenerator(name = "assigned_id", strategy = "com.decathlon.sino.data.entity.basic.AssignedIdentityGenerator")
    @GeneratedValue(generator = "assigned_id", strategy = GenerationType.IDENTITY)
    @Column(length = 12)
    protected Long id;

}

