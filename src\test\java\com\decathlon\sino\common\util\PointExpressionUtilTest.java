package com.decathlon.sino.common.util;

import org.junit.runner.RunWith;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.junit.jupiter.api.Test;
import static org.junit.Assert.assertTrue;

import com.decathlon.sino.App;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringJUnit4ClassRunner.class)
@EnableAutoConfiguration
@SpringBootTest(classes = App.class)
@Slf4j
public class PointExpressionUtilTest {

	@Test
	public void testHistoryPointBalanceLessThanMinus10000WithSpEL() {
		int historyPointBalance = -15000;
		StandardEvaluationContext context = new StandardEvaluationContext();
		context.setVariable("historyPointBalance", historyPointBalance);
		ExpressionParser parser = new SpelExpressionParser();
		Boolean result = parser.parseExpression("#historyPointBalance < -10000").getValue(context, Boolean.class);
		assertTrue(result);
	}

}
