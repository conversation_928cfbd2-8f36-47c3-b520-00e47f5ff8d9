package com.decathlon.sino.data.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.decathlon.sino.data.entity.basic.IdEntity;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@SuperBuilder
@Table(name = "rc_list_import")
public class RcListImportEntity extends IdEntity implements Serializable{

	private static final long serialVersionUID = 1L;
	
	private Long personId;
	private String cardNumber;
	private String mobile;
	private String reason;
	private String operator;
	private Date createdTime;
	private Date updatedTime;
	// 生效时间
	private Date effectiveStartTime;
	// 失效时间
	private Date effectiveEndTime;
	// 全局类型, 场景类型
	private String type;
	// true：白名单，false:黑名单
	private Boolean status;

}
