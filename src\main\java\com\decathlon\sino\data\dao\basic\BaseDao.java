package com.decathlon.sino.data.dao.basic;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;

import com.decathlon.sino.data.entity.basic.IdEntity;

@NoRepositoryBean
public interface BaseDao<E extends IdEntity> extends JpaRepository<E, Long>, JpaSpecificationExecutor<E> {

}
