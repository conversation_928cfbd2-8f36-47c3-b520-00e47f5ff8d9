package com.decathlon.sino.kafka.biz.helper;



import com.decathlon.sino.common.constant.PosdataDifinition;
import com.decathlon.sino.common.constant.purchase.Channel;
import com.decathlon.sino.common.exception.PoslogWrongDataException;
import com.decathlon.sino.common.exception.ServiceException;
import com.decathlon.sino.model.biz.ItemInfoBpn;
import com.decathlon.sino.model.biz.PoslogBusinessUnit;
import com.decathlon.sino.model.biz.PoslogItemDetail;
import com.decathlon.sino.model.biz.PoslogPaymentDetail;
import com.decathlon.sino.model.bo.OfflineRefundBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.nrf_arts.ixretail.v6_0_0.poslog.*;
import org.springframework.util.CollectionUtils;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import static com.decathlon.sino.common.exception.SystemError.NOT_MEMBER_ERROR;

/**
 * <AUTHOR> Lei
 * @create 2019-07-11
 */
@Slf4j
public class PoslogAnalyzeHelper {


    public static POSLogBase getPoslogBase(String xmlContent) throws JAXBException {
// analyse poslog, assemble POSLogBase object
        final StringReader sr = new StringReader(xmlContent);
        final JAXBContext jaxbContext = JAXBContext.newInstance(POSLogBase.class);
        final Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
        final POSLogBase posLogBase = (POSLogBase) ((javax.xml.bind.JAXBElement) jaxbUnmarshaller
                .unmarshal(sr)).getValue();
     /**
      *  check if exist one Transaction in poslog
      *  check content in labels: <Transaction><Transaction/>
      */
        final boolean isTransactionValid = posLogBase.getTransaction() != null
                && posLogBase.getTransaction().size() == 1
                && !StringUtils.isEmpty(posLogBase.getTransaction().get(0).getTransactionID());

        // if no Transaction or more than 1 Transaction, save a event with failed status
        if (!isTransactionValid) {
            throw new PoslogWrongDataException();
        }

        return posLogBase;
    }



    public static String anaylyzeTransactionStatus(TransactionDomainSpecific purchase) {
        // acquire RetailTransaction
        RetailTransactionDomainSpecific retailTransactionDomainSpecific = purchase
                .getRetailTransaction().get(0);

        // acquire transaction status
        return retailTransactionDomainSpecific.getTransactionStatus();
    }

    public static String analyzeSpecificChanel(TransactionDomainSpecific purchase){
        String chanel = analyzeChannel(purchase);
        if (PosdataDifinition.CHANNEL_PHYSICAL.equals(chanel)){
            return Channel.PHYSICAL.name();
        }
        String storeNumber = analyzeStoreNumber(purchase);
        return Channel.getByPrefix(storeNumber).name();
    }


    /**
     *  physical / digital
     * @param purchase
     * @return
     */
    public static String analyzeChannel(TransactionDomainSpecific purchase){
        List<ChannelCommonData> channelCommonDataList = purchase.getChannel();
        if (channelCommonDataList == null || channelCommonDataList.size() == 0) {
            return null;
        }
        return channelCommonDataList.get(0).getTouchPointID().getPhysicalDigitalCode();
    }

    /**
     * fill value of iteminfo from returnBase
     *//*
  public static void analyzeItemInfo(ReturnBase returnBase, ItemInfoForPoint itemInfo)
      throws PoslogItemException {

    List<Object> list = returnBase.getPOSIdentityOrItemIDOrSpecialOrderNumber();
    if (list == null || list.size() == 0) {
      throw new PoslogItemException();
    }
    for (Object o : list) {
      if (o instanceof ItemBase.ItemID) {
        ItemBase.ItemID itemId = (ItemBase.ItemID) o;
        itemInfo.setItemCode(itemId.getValue());
        break;
      }
    }
    if (itemInfo.getItemCode() == null || "".equals(itemInfo.getItemCode())) {
      throw new PoslogItemException();
    }
    try {
      itemInfo.setQuantity(Integer.valueOf("-" + returnBase.getQuantity().get(0).getValue()));
    } catch (Exception e) {
      log.error("", e);
      throw new PoslogItemException();
    }
  }*/

    /**
     * fill value of iteminfo from saleBase
     */

    public static ItemInfoBpn analyzeItemInfo(ItemDomainSpecific base, String transactionId)
            throws PoslogWrongDataException {

        ItemInfoBpn itemInfo = new ItemInfoBpn();
        List<Object> list = base.getPOSIdentityOrItemIDOrSpecialOrderNumber();
        if (list == null || list.size() == 0) {
            throw new PoslogWrongDataException();
        }

        //定义itemCode
        String itemCode = "";
        for (Object o : list) {
            if (o instanceof ItemBase.ItemID) {
                ItemBase.ItemID itemId = (ItemBase.ItemID) o;
                itemCode= itemId.getValue();
                itemInfo.setItemId(itemCode);
                break;
            }
        }
        if (itemCode == null || "".equals(itemCode)) {
            throw new PoslogWrongDataException();
        }
        //旧逻辑取poslog里extendedAmount，extendedAmount定义为商家应收的渠道的单个商品的用户实付需要从msgbus取
        BigDecimal itemPaidFees = base.getExtendedAmount().getValue();
        log.info("transactionId {} itemCode {} get item fee extended amount is {}"
                ,transactionId,itemCode, itemPaidFees);

        try {
            double singlePrice = itemPaidFees
                    .divide(base.getQuantity().get(0).getValue(), 2, BigDecimal.ROUND_HALF_EVEN)
                    .doubleValue();
            itemInfo.setPrice(singlePrice);
            BigDecimal value = base.getQuantity().get(0).getValue();
            if (base instanceof ReturnBase) {
                itemInfo.setItemTotalPrice(itemPaidFees.negate().doubleValue());
                itemInfo.setQuantity(value.negate().intValue());
            } else if (base instanceof SaleBase) {
                itemInfo.setItemTotalPrice(itemPaidFees.doubleValue());
                itemInfo.setQuantity(value.intValue());
            }
            //      itemInfo.setQuantity(Integer.valueOf("" +));
        } catch (Exception e) {
            log.error("", e);
            throw new PoslogWrongDataException();
        }

        // 加上originTransactionId
        boolean hasOriginTransactionId = base.getTransactionLink() != null && !base.getTransactionLink().isEmpty()
                && !base.getTransactionLink().get(0).getTransactionIDOrBusinessUnitAndWorkstationID().isEmpty();
        if (hasOriginTransactionId) {
            String originTransactionId = String.valueOf(base.getTransactionLink().get(0)
                    .getTransactionIDOrBusinessUnitAndWorkstationID().get(0));
            itemInfo.setOriginTransactionId(originTransactionId);
        }
        return itemInfo;
    }

    public static String originTransactionIds(List<ItemInfoBpn> returnPartItems){
        // 加上originTransactionId
        Set<String> collect = returnPartItems.stream().map(ItemInfoBpn::getOriginTransactionId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(collect)){
            return "";
        }

        return String.join(",",collect);
    }




    /**
     * fill value of iteminfo from saleBase
     */
    @Deprecated
    public static void analyzeItemInfo(SaleBase saleBase, ItemInfoBpn itemInfo)
            throws PoslogWrongDataException {

        List<Object> list = saleBase.getPOSIdentityOrItemIDOrSpecialOrderNumber();
        if (list == null || list.size() == 0) {
            throw new PoslogWrongDataException();
        }
        for (Object o : list) {
            if (o instanceof ItemBase.ItemID) {
                ItemBase.ItemID itemId = (ItemBase.ItemID) o;
                itemInfo.setItemId(itemId.getValue());
                break;
            }
        }
        if (itemInfo.getItemId() == null || "".equals(itemInfo.getItemId())) {
            throw new PoslogWrongDataException();
        }
        try {
            itemInfo.setPrice(Double.parseDouble("" + saleBase.getExtendedAmount().getValue()) / Integer
                    .valueOf(saleBase.getQuantity().get(0).getValue() + ""));
            itemInfo.setItemTotalPrice(Double.parseDouble("" + saleBase.getExtendedAmount().getValue()));
            itemInfo.setQuantity(Integer.valueOf("" + saleBase.getQuantity().get(0).getValue()));
        } catch (Exception e) {
            log.error("", e);
            throw new PoslogWrongDataException();
        }
    }



    public static List<PoslogItemDetail> analyzePoslogItems(
            TransactionDomainSpecific transactionDomainSpecific) throws PoslogWrongDataException {

        RetailTransactionDomainSpecific retailTransactionDomainSpecific = transactionDomainSpecific
                .getRetailTransaction().get(0);
        List<LineItemDomainSpecific> list = retailTransactionDomainSpecific.getLineItem();
        if (list == null || list.isEmpty()) {
            throw new PoslogWrongDataException();
        }
        List<PoslogItemDetail> items = new ArrayList<>();
        for (LineItemDomainSpecific lineItem : list) {
            SaleBase saleBase = lineItem.getSale();
            if (saleBase == null) {
                continue;
            }
            PoslogItemDetail poslogItemDetail = new PoslogItemDetail();
            anlyzeReceiptsDetail(saleBase, poslogItemDetail);
            poslogItemDetail.setLabel(saleBase.getDescription().get(0).getValue());
            items.add(poslogItemDetail);
        }

        for (LineItemDomainSpecific lineItem : list) {
            ReturnBase returnBase = lineItem.getReturn();
            if (returnBase == null) {
                continue;
            }
            PoslogItemDetail poslogItemDetail = new PoslogItemDetail();
            anlyzeReceiptsDetail(returnBase, poslogItemDetail);
            poslogItemDetail.setLabel(returnBase.getDescription().get(0).getValue());
            items.add(poslogItemDetail);
        }
        if (items == null || items.isEmpty()) {
            throw new PoslogWrongDataException();
        }

        return items;
    }


    public static void anlyzeReceiptsDetail(SaleBase saleBase, PoslogItemDetail poslogItemDetail)
            throws PoslogWrongDataException {
        List<Object> list = saleBase.getPOSIdentityOrItemIDOrSpecialOrderNumber();
        if (list == null || list.isEmpty()) {
            throw new PoslogWrongDataException();
        }
        for (Object o : list) {
            if (o instanceof ItemBase.ItemID) {
                ItemBase.ItemID itemId = (ItemBase.ItemID) o;
                poslogItemDetail.setItemId(itemId.getValue());
                break;
            }
        }
        poslogItemDetail.setTotalPrice("" + saleBase.getExtendedAmount().getValue());
        List<TicketSerialNumberType> ticketSerialNumberTypes = saleBase.getSerialNumber();
        if (ticketSerialNumberTypes != null && !ticketSerialNumberTypes.isEmpty()) {
            for (TicketSerialNumberType ticketSerialNumberType : ticketSerialNumberTypes) {
                if (ticketSerialNumberType.getType().equals(PosdataDifinition.RFID)) {
                    poslogItemDetail.setItemRfId(ticketSerialNumberType.getValue());
                }
            }
        } else {
            log.warn("RFID IS NOT EXIST");
        }
        poslogItemDetail
                .setUnitaryAmount(String.valueOf(saleBase.getRegularSalesUnitPrice().getValue()));
        poslogItemDetail.setQuantity(saleBase.getQuantity().get(0).getValue().intValue());
        poslogItemDetail.setIntModelCode("");
        poslogItemDetail.setExtModelCode("");
        poslogItemDetail.setExtArticleCode("");
        poslogItemDetail.setUniversCode("");
        poslogItemDetail.setArticleCode("");
        poslogItemDetail.setIntArticleCode(poslogItemDetail.getItemId());

        List<ItemTaxType> itemTaxTypes = saleBase.getTax();
        if (itemTaxTypes == null || itemTaxTypes.isEmpty()) {
            return;
        }
        for (ItemTaxType itemTaxType : itemTaxTypes) {
            if (itemTaxType.getTaxableAmount() != null) {
                poslogItemDetail.setTotalPriceTaxEx("" + itemTaxType.getTaxableAmount().getValue());
            }
        }
    }


    public static void anlyzeReceiptsDetail(ReturnBase returnBase, PoslogItemDetail poslogItemDetail)
            throws PoslogWrongDataException {
        List<Object> list = returnBase.getPOSIdentityOrItemIDOrSpecialOrderNumber();
        if (list == null || list.isEmpty()) {
            throw new PoslogWrongDataException();
        }
        for (Object o : list) {
            if (o instanceof ItemBase.ItemID) {
                ItemBase.ItemID itemId = (ItemBase.ItemID) o;
                poslogItemDetail.setItemId(itemId.getValue());
                break;
            }
        }
        poslogItemDetail.setTotalPrice("-" + returnBase.getExtendedAmount().getValue());
        List<ItemTaxType> itemTaxTypes = returnBase.getTax();
        if (itemTaxTypes == null || itemTaxTypes.isEmpty()) {
            throw new PoslogWrongDataException();
        }
        for (ItemTaxType itemTaxType : itemTaxTypes) {
            if (itemTaxType.getTaxableAmount() != null) {
                poslogItemDetail.setTotalPriceTaxEx("-" + itemTaxType.getTaxableAmount().getValue());
            }
        }

        List<TicketSerialNumberType> ticketSerialNumberTypes = returnBase.getSerialNumber();
        if (ticketSerialNumberTypes != null && !ticketSerialNumberTypes.isEmpty()) {
            for (TicketSerialNumberType ticketSerialNumberType : ticketSerialNumberTypes) {
                if (ticketSerialNumberType.getType().equals(PosdataDifinition.RFID)) {
                    poslogItemDetail.setItemRfId(ticketSerialNumberType.getValue());
                }
            }
        } else {
            log.warn("RFID IS NOT EXIST");
        }
        poslogItemDetail
                .setUnitaryAmount(String.valueOf(returnBase.getRegularSalesUnitPrice().getValue()));
        poslogItemDetail.setQuantity(returnBase.getQuantity().get(0).getValue().negate().intValue());
        poslogItemDetail.setIntModelCode("");
        poslogItemDetail.setExtModelCode("");
        poslogItemDetail.setExtArticleCode("");
        poslogItemDetail.setUniversCode("");
        poslogItemDetail.setArticleCode("");
        poslogItemDetail.setIntArticleCode(poslogItemDetail.getItemId());
    }


    public static PoslogBusinessUnit analyzeBusinessUnit(
            TransactionDomainSpecific transactionDomainSpecific) throws PoslogWrongDataException {
        /**
         * already check retail transaction, so here no need to chack again
         */
        PoslogBusinessUnit poslogBusinessUnit = new PoslogBusinessUnit();
        List<BusinessUnitComplexType> businessUnitComplexTypes = transactionDomainSpecific
                .getBusinessUnit();
        if (businessUnitComplexTypes == null || businessUnitComplexTypes.isEmpty()) {
            throw new PoslogWrongDataException();
        }
        poslogBusinessUnit.setCountryCode(businessUnitComplexTypes.get(1).getUnitID().getName());
        poslogBusinessUnit.setStoreName(businessUnitComplexTypes.get(0).getUnitID().getName());
        String storeValue = businessUnitComplexTypes.get(0).getUnitID().getValue().trim();
        if (storeValue == null || storeValue.isEmpty()) {
            throw new PoslogWrongDataException();
        }
        String[] thridData = storeValue.split("-");
        poslogBusinessUnit.setThirdType(thridData[0]);
        poslogBusinessUnit.setThirdNumber(thridData[1]);
        poslogBusinessUnit.setSubThirdNumber(thridData[2]);
        return poslogBusinessUnit;
    }


    public static String analyzeCashierNumber(TransactionDomainSpecific transactionDomainSpecific)
            throws PoslogWrongDataException {
        /**
         * already check retail transaction, so here no need to chack again
         */
        RetailTransactionDomainSpecific retailTransactionDomainSpecific = transactionDomainSpecific
                .getRetailTransaction().get(0);
        List<CustomerDomainSpecific> customerDomainSpecific = retailTransactionDomainSpecific
                .getCustomer();
        if (customerDomainSpecific == null || customerDomainSpecific.size() == 0) {
            throw new PoslogWrongDataException();
        }

        return customerDomainSpecific.get(0).getCustomerID();
    }




    public static List<PoslogPaymentDetail> analyzePoslogPaymentDetail(
            TransactionDomainSpecific transactionDomainSpecific)
            throws PoslogWrongDataException {
        /**
         * already check retail transaction, so here no need to chack again
         */
        RetailTransactionDomainSpecific retailTransactionDomainSpecific = transactionDomainSpecific
                .getRetailTransaction().get(0);
        List<LineItemDomainSpecific> list = retailTransactionDomainSpecific.getLineItem();
        //    if (list == null || list.isEmpty()) {
        //      throw new PoslogItemException();
        //    }
        List<PoslogPaymentDetail> poslogPaymentDetails = new ArrayList<>();
        for (LineItemDomainSpecific lineItem : list) {
            TenderBase tenderBase = lineItem.getTender();
            if (tenderBase == null) {
                continue;
            }
            PoslogPaymentDetail poslogPaymentDetail = new PoslogPaymentDetail();
            analyzePoslogPaymentDetail(tenderBase, poslogPaymentDetail);
            poslogPaymentDetail
                    .setPaymentDate(transactionDomainSpecific.getPOSLogDateTime().get(0).getValue() + "");
            poslogPaymentDetails.add(poslogPaymentDetail);

        }

        //    if (poslogPaymentDetails == null || poslogPaymentDetails.isEmpty()) {
        //      throw new PoslogPaymentException();
        //    }
        return poslogPaymentDetails;
    }

    public static void analyzePoslogPaymentDetail(TenderBase tenderBase,
                                                  PoslogPaymentDetail poslogPaymentDetail)
            throws PoslogWrongDataException {
        if (tenderBase == null || tenderBase.getTenderType() == null || tenderBase.getAmount() == null
                || tenderBase.getTypeCode() == null) {
            throw new PoslogWrongDataException();
        }
        String[] split = tenderBase.getTenderType().split(":");
        if (split.length >= 2) {
            poslogPaymentDetail.setPaymentMode(split[1]);
        }
        poslogPaymentDetail.setCurrency(tenderBase.getAmount().getCurrency());
        poslogPaymentDetail.setPaymentCode(tenderBase.getReasonCode().getValue());
        if (tenderBase.getTypeCode().equals(PosdataDifinition.REFUND)) {
            poslogPaymentDetail.setAmount("-" + tenderBase.getAmount().getValue());
        } else {
            poslogPaymentDetail.setAmount("" + tenderBase.getAmount().getValue());
        }

    }

    /**
     * read transaction date from poslog
     */
    public static String analyzeCustomerId(TransactionDomainSpecific purchase)
            throws PoslogWrongDataException {
        /**
         * already check retail transaction, so here no need to chack again
         */
            RetailTransactionDomainSpecific retailTransactionDomainSpecific = purchase
                    .getRetailTransaction()
                    .get(0);


        List<CustomerDomainSpecific> customerDomainSpecific = retailTransactionDomainSpecific
                .getCustomer();
        if (CollectionUtils.isEmpty(customerDomainSpecific)) {
            throw new ServiceException(NOT_MEMBER_ERROR);
        }

        String customerId = customerDomainSpecific.get(0).getCustomerID();
        if (StringUtils.isBlank(customerId)) {
            throw new ServiceException(NOT_MEMBER_ERROR);
        }

        return customerId;
    }


    /**
     * read transaction date from poslog
     */
    public static Date analyzeTransactionDate(TransactionDomainSpecific purchase)
            throws PoslogWrongDataException {
        List<TransactionBase.POSLogDateTime> dateTimeCommonData = purchase.getPOSLogDateTime();

        if (CollectionUtils.isEmpty(dateTimeCommonData)) {
            throw new PoslogWrongDataException();
        }
        Date date = null;
        for (DateTimeCommonData dtc : dateTimeCommonData) {
            if (dtc.getTypeCode() == null) {
                throw new PoslogWrongDataException();
            }
            if (PosdataDifinition.POSLOG_DATETIME_TRANSACTION.equals(dtc.getTypeCode())) {
                date = dtc.getValue().toGregorianCalendar().getTime();
                break;
            }
        }
        if (date == null) {
            throw new PoslogWrongDataException();
        } else {
            return date;
        }
    }

    /**
     * read transaction date which type code is message from poslog
     */
    public static Date analyzeTxMessageDate(TransactionDomainSpecific purchase)
            throws PoslogWrongDataException {
        List<TransactionBase.POSLogDateTime> dateTimeCommonData = purchase.getPOSLogDateTime();

        if (dateTimeCommonData == null || dateTimeCommonData.isEmpty()) {
            throw new PoslogWrongDataException();
        }
        Date date = null;
        for (DateTimeCommonData dtc : dateTimeCommonData) {
            if (dtc.getTypeCode() == null) {
                throw new PoslogWrongDataException();
            }
            if (PosdataDifinition.POSLOG_DATETIME_MESSAGE.equals(dtc.getTypeCode())) {
                date = dtc.getValue().toGregorianCalendar().getTime();
                break;
            }
        }
        if (date == null) {
            throw new PoslogWrongDataException();
        } else {
            return date;
        }
    }
    public static String analyzeSequnceNumber(TransactionDomainSpecific purchase) throws PoslogWrongDataException {
        long sequnceNumber = purchase.getSequenceNumber().longValue();
        if (sequnceNumber <= 0) {
            throw new PoslogWrongDataException();
        }

        return String.valueOf(sequnceNumber);
    }

     public static String analyzeSpecialOrderNumber(TransactionDomainSpecific purchase) throws PoslogWrongDataException {
        String specialOrderNumber = purchase.getRetailTransaction().get(0).getSpecialOrderNumber();
        if (StringUtils.isBlank(specialOrderNumber)) {
            throw new PoslogWrongDataException();
        }

        return specialOrderNumber;
    }

  /*public static String analyzeTransactionDate(TransactionDomainSpecific purchase)
      throws PoslogDateException {
    List<TransactionBase.POSLogDateTime> poslogDateTimeLists = purchase.getPOSLogDateTime();
    if (CollectionUtils.isEmpty(poslogDateTimeLists)) {
      throw new PoslogDateException();
    }

    XMLGregorianCalendar poslogTranscationDate = null;
    for (POSLogDateTime posLogDateTime : poslogDateTimeLists) {
      if (CommonDefine.POSLOG_DATA_CODE_TYPE.equals(posLogDateTime.getTypeCode())) {
        poslogTranscationDate = posLogDateTime.getValue();
      }
    }

    if (poslogTranscationDate == null) {
      throw new PoslogDateException();
    } else {
      String transcationData = poslogTranscationDate.toString();
      if (StringUtils.isEmpty(transcationData)) {
        throw new PoslogDateException();
      }
      return transcationData;
    }

  }*/

    /**
     * read storenumber from poslog
     */
    public static String analyzeStoreNumber(TransactionDomainSpecific purchase)
            throws PoslogWrongDataException {
        List<BusinessUnitComplexType> unitList = purchase.getBusinessUnit();
        if (unitList == null || unitList.size() == 0) {
            throw new PoslogWrongDataException();
        }

        String storeNumber = null;
        for (BusinessUnitComplexType businessUnitComplexType : unitList) {
            if (null == businessUnitComplexType.getUnitID() || null == businessUnitComplexType.getUnitID()
                    .getTypeCode()) {
                throw new PoslogWrongDataException();
            }
            String type = businessUnitComplexType.getUnitID().getTypeCode();

            //      if (PosdataDifinition.UNITID_TYPECODE_RETAILSTORE.equals(type) ||
            //          PosdataDifinition.UNITID_TYPECODE_WEBSITE.equals(type)) {
            storeNumber = businessUnitComplexType.getUnitID().getValue();
            break;
            //      }
        }
        if (storeNumber == null) {
            throw new PoslogWrongDataException();
        } else {
            return storeNumber;
        }
    }

    /**
     * reag total price from poslog
     */
    public static Double analyzeTotalPrice(TransactionDomainSpecific purchase)
            throws PoslogWrongDataException {
        try {
            /**
             * already check retail transaction, so here no need to chack again
             */
            RetailTransactionDomainSpecific retailTransactionDomainSpecific = purchase
                    .getRetailTransaction().get(0);
            List<TransactionTotalBase> transactionTotalBases = retailTransactionDomainSpecific.getTotal();
            if (transactionTotalBases == null || transactionTotalBases.size() == 0) {
                throw new PoslogWrongDataException();
            }
            Double amount = null;
            for (TransactionTotalBase transactionTotalBase : transactionTotalBases) {
                String type = transactionTotalBase.getTotalType();
                String typeCode = transactionTotalBase.getTypeCode();
                if (PosdataDifinition.TOTALTYPE_TRANSACTIONGRANDAMOUNT.equals(type)
                        && PosdataDifinition.TOTALTYPE_SALE_RETURN.equals(typeCode)) {
                    if (null == transactionTotalBase.getValue()) {
                        throw new PoslogWrongDataException();
                    }
                    amount = new Double(transactionTotalBase.getValue().doubleValue());
                    break;
                } else if (PosdataDifinition.TOTALTYPE_TRANSACTIONGRANDAMOUNT.equals(type)
                        && PosdataDifinition.TOTALTYPE_RETURN.equals(typeCode)) {
                    if (null == transactionTotalBase.getValue()) {
                        throw new PoslogWrongDataException();
                    }
                    amount = -1 * Math.abs(new Double(transactionTotalBase.getValue().doubleValue()));
                    break;
                }
            }
            if (amount == null) {
                throw new PoslogWrongDataException();
            } else {
                return amount.doubleValue();
            }
        } catch (Exception e) {
            throw new PoslogWrongDataException();
        }
    }



    public static List<ItemInfoBpn> analyzeItemInfos(TransactionDomainSpecific purchase, boolean includeSale, boolean includeReturn)
            throws PoslogWrongDataException {
        /**
         * already check retail transaction, so here no need to chack again
         */
        RetailTransactionDomainSpecific retailTransactionDomainSpecific = purchase
                .getRetailTransaction().get(0);
        List<LineItemDomainSpecific> list = retailTransactionDomainSpecific.getLineItem();
        if (list == null || list.size() == 0) {
            throw new PoslogWrongDataException();
        }


        List<ItemInfoBpn> itemInfos = new ArrayList<>();
        for (LineItemDomainSpecific lineItem : list) {
            ItemInfoBpn itemInfo = null;


            if (lineItem.getSale() != null && includeSale) {
                itemInfo = analyzeItemInfo(lineItem.getSale(), purchase.getTransactionID());


            } else if (lineItem.getReturn() != null && includeReturn) {
                itemInfo = analyzeItemInfo(lineItem.getReturn(), purchase.getTransactionID());
            } else {
                continue;
            }


            List<JAXBElement<?>> sequenceNumberAndBeginDateTimeAndEndDateTime = lineItem
                    .getSequenceNumberAndBeginDateTimeAndEndDateTime();
            for (JAXBElement<?> ele : sequenceNumberAndBeginDateTimeAndEndDateTime) {
                if (ele.getName() == null) {
                    throw new PoslogWrongDataException();
                }
                if (PosdataDifinition.SEQUENCENUMBER.equals(ele.getName().getLocalPart())) {
                    itemInfo.setLineNumber(String.valueOf(ele.getValue()));
                    break;
                }
            }
            itemInfos.add(itemInfo);
        }
        return itemInfos;
    }


    /**
     * read return transaction detailed products infos from poslog
     */
    public static  List<ItemInfoBpn> analyzeAllItemInfos(TransactionDomainSpecific purchase)
            throws PoslogWrongDataException {

        return analyzeItemInfos(purchase, true, true);
    }


}
