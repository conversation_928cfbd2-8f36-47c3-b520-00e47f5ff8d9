package com.decathlon.sino.kafka.biz;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DefaultKafkaListener implements KafkaListenerStrategy {
    @Override
    public void handleMessage(ConsumerRecord<String, Object> records) {
        try {
            log.info("Processing message: {}", records.value());
        } catch (Exception e) {
            log.error("Error processing message: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public String getBizName() {
        return "DEFAULT";
    }

    @Override
    public String getDescription() {
        return "Default message processing strategy";
    }
} 