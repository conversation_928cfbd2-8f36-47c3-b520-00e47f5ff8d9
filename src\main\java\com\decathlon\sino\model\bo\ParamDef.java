package com.decathlon.sino.model.bo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@JsonNaming(SnakeCaseStrategy.class)
@AllArgsConstructor
public class ParamDef {
	
	private String name;
	private Object defaultValue;
	private Boolean enableConfig;

}
