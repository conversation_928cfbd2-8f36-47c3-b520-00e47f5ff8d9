package com.decathlon.sino.common.util;

import static org.junit.jupiter.api.Assertions.*;

import java.math.BigDecimal;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.decathlon.sino.App;

import lombok.extern.slf4j.Slf4j;

@RunWith(SpringJUnit4ClassRunner.class)
@EnableAutoConfiguration
@SpringBootTest(classes = App.class)
@Slf4j
class ExpressionUtilTest {

	
	/**
	 * test boolean expression
	 */
	@Test
	void test_expression_bool() {
		String expression = "#amount > 1000 && #age >= 18";
		Map<String, Object> data =  Map.of("amount", 2000, "age", 20);
		ExpressionUtil expressionUtil = new ExpressionUtil();
		BigDecimal result = expressionUtil.evaluateExpression(expression, data);
		assertEquals(BigDecimal.ONE, result);
	}
	
	/**
	 * test string length expression
	 */
//	@Test
	void test_expression_string_length() {
		String expression = "#str.length() == #length";
		Map<String, Object> data =  Map.of("str", "ddddd", "length", 2);
		ExpressionUtil expressionUtil = new ExpressionUtil();
		BigDecimal result = expressionUtil.evaluateExpression(expression, data);
		assertEquals(BigDecimal.ZERO, result);
	}
	
	/**
	 * test string contains expression
	 */
//	@Test
	void test_expression_string_contains() {
		String expression = "#str != null && #str.contains(#substring)";
		Map<String, Object> data =  Map.of("str", "dddxxd", "substring", "dd");
		ExpressionUtil expressionUtil = new ExpressionUtil();
		BigDecimal result = expressionUtil.evaluateExpression(expression, data);
		assertEquals(BigDecimal.ONE, result);
	}

}
