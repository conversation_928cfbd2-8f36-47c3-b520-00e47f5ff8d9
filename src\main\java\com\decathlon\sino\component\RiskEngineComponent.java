package com.decathlon.sino.component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.decathlon.sino.common.util.ExpressionUtil;
import com.decathlon.sino.data.dao.RcBlackListAuditEntityRepository;
import com.decathlon.sino.data.dao.RcBlackListEntityRepository;
import com.decathlon.sino.data.dao.RcBlackListReasonHitEntityRepository;
import com.decathlon.sino.data.entity.RcBlackListAuditEntity;
import com.decathlon.sino.data.entity.RcBlackListEntity;
import com.decathlon.sino.data.entity.RcBlackListReasonHitEntity;
import com.decathlon.sino.model.bo.RuleDefinition;
import com.decathlon.sino.model.input.RiskInfoDto;
import com.decathlon.sino.model.bo.RiskResultDto;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * audit engine 1. through rule cache, get all rules by bizType and objectType
 * 2. audit each rule 
 * 3. get the raw score by dsl engine 
 * 4. get the weighted score by multiplying raw score and weight 
 * 5. compare the weighted score with threshold 
 * 6. if the weighted score is greater than or equal to threshold, it means hit 
 * 7. if hit, save the result to rc_black_list_reason_hit table
 */
@Component
@AllArgsConstructor
@Slf4j
public class RiskEngineComponent {

	private final RuleCacheComponent ruleCacheComponent;
	private final RcBlackListReasonHitEntityRepository rcBlackListReasonHitEntityRepository;
	private final RcBlackListAuditEntityRepository rcBlackListAuditEntityRepository;
	private final RcBlackListEntityRepository rcBlackListEntityRepository;

	public RiskInfoDto evaluate(String bizType, String obejctId, String objectType, Map<String, Object> ctx,String operator, Boolean isAudit) {
		RiskInfoDto riskInfoDto = new RiskInfoDto();
		List<RiskResultDto> riskResultDtos = new ArrayList<>();
		// get all rules by bizType and objectType
		for (RuleDefinition rule : ruleCacheComponent.getAllByBizAndObj(bizType, objectType)) {
			RiskResultDto riskResultDto = new RiskResultDto();
			Map<String, Object> vars = new HashMap<>();
			rule.getParams().forEach(p ->{
				if(Boolean.TRUE.equals(p.getEnableConfig())) {
					vars.put(p.getName(), p.getDefaultValue());
				}
			});
			// depending on the rule parameter configuration, used the context to override
			// the default value
			vars.putAll(ctx);
			// audit each rule definition
			BigDecimal rawScore = ExpressionUtil.evaluateExpression(rule.getExpr(), vars);
			// get the weighted score by multiplying raw score and weight
			BigDecimal weighted = rawScore.multiply(rule.getWeight());
			// compare the weighted score with threshold
			Boolean hit = weighted.compareTo(rule.getThreshold()) >= 0;
			if (Boolean.TRUE.equals(hit)) {
				// if hit, save the result to rc_black_list_reason_hit table
				RcBlackListReasonHitEntity rcBlackListReasonHitEntity = new RcBlackListReasonHitEntity();
				rcBlackListReasonHitEntityRepository.save(rcBlackListReasonHitEntity);
				// check if opened audit
				if (Boolean.TRUE.equals(isAudit)) {
					// save the audit result to rc_black_list_audit table
					RcBlackListAuditEntity rcBlackListAuditEntity = new RcBlackListAuditEntity();
					rcBlackListAuditEntityRepository.save(rcBlackListAuditEntity);
					// save the audit result to rc_black_list_audit table
					RcBlackListEntity rcBlackListEntity = new RcBlackListEntity();
					rcBlackListEntity.setObjectId(obejctId);
					rcBlackListEntityRepository.save(rcBlackListEntity);
				}
			}
			riskResultDto.setScore(rawScore);
			riskResultDtos.add(riskResultDto);
		}
		riskInfoDto.setRiskResult(riskResultDtos);
		return riskInfoDto;
	}

}
