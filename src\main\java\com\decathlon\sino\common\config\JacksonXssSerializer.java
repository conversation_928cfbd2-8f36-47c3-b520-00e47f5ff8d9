package com.decathlon.sino.common.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import org.springframework.web.util.HtmlUtils;

import java.io.IOException;

public class JacksonXssSerializer extends StdSerializer<String> {

    private static final long serialVersionUID = -2140954964196376424L;

    public JacksonXssSerializer() {
        super(String.class);
    }

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        String safe = JsoupUtil.clean(value);
        safe = HtmlUtils.htmlUnescape(safe);
        gen.writeString(safe);
    }
}
