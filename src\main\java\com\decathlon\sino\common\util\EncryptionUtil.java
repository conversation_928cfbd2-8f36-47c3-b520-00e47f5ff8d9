package com.decathlon.sino.common.util;

import org.apache.commons.codec.binary.Hex;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * The Class EncryptionUtils.
 */
public class EncryptionUtil {


    public static String encryptWithAES(String secret, String iv, String content) {
        try {
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(), "AES");
            IvParameterSpec paramSpec = new IvParameterSpec(iv.getBytes());
            Cipher cipher = Cipher.getInstance("AES/CFB/NOPADDING");
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, paramSpec);
            byte[] result = cipher.doFinal(content.getBytes());
            return Hex.encodeHexString(result);
        } catch (Exception e) {
            throw new RuntimeException("Encrypt fail");
        }
    }

    public static void main(String[] args) {

        System.out.println(EncryptionUtil.encryptWithAES("qA8MjR3p?7WkM#*9e?fySTVG6uMr2g9b", "ddfsfsf", "1715314226"));
    }


}
