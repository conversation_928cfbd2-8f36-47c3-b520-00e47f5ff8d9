package com.decathlon.sino.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 */

@Slf4j
@Component
public class LocalBase64Utils {

    @Value("${v2.secret:3yNMEfFQw4XxEy77ojMeZ7JRkhHAXaBZUMwjFCiDXDDb3gzxZ5rY9DfPjpZu5PXZ}")
    private String v2Secret;

    private static final Base64 base64 = new Base64();

    public static String decodedBase64(String data) throws UnsupportedEncodingException {
        log.info("decoded base 64 data:{}", data);
        return new String(base64.decode(data), "UTF-8");
    }

    public static String encodedBase64(String data) {
        log.info("encode base 64 data:{}", data);
        return new String(base64.encode(data.getBytes()));
    }


    public static String md516Lower(String data) {
        String md532Lower = DigestUtils.md5DigestAsHex(data.getBytes());
        return md532Lower.substring(8, 24).toUpperCase();
    }

    public String getMd516Lower() {
        String md532Lower = DigestUtils.md5DigestAsHex(v2Secret.getBytes());
        return md532Lower.substring(8, 24).toUpperCase();
    }

}
