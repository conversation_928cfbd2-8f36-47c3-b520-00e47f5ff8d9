package com.decathlon.sino.service.impl;

import java.util.Map;

import org.springframework.stereotype.Service;

import com.decathlon.sino.component.RiskEngineComponent;
import com.decathlon.sino.data.dao.RcBlackListEntityRepository;
import com.decathlon.sino.data.dao.RcBlacklistGlobalEntityRepository;
import com.decathlon.sino.model.input.RiskInfoDto;
import com.decathlon.sino.service.AuditService;
import com.decathlon.sino.service.impl.helper.PointRiskHelper;

import cn.hutool.json.JSONObject;


@Service
public class PointAuditServiceImpl extends AduitProcessService implements AuditService{
	
	public PointAuditServiceImpl(RiskEngineComponent riskEngineComponent,RcBlackListEntityRepository rcBlackListEntityRepository,RcBlacklistGlobalEntityRepository rcGlobalBlackListEntityRepository) {
		super(riskEngineComponent,rcBlackListEntityRepository,rcGlobalBlackListEntityRepository);
	}


	@Override
	public RiskInfoDto checkRisk(String bizType, String obejctId, String objectType, JSONObject eventData,String operator, Boolean isAudit) {
		if(Boolean.TRUE.equals(isAudit)) {
			return this.auditRisk(bizType, obejctId, objectType, eventData, operator, isAudit);
		}else {
			return super.checkRisk(bizType, obejctId, objectType);
		}
	}
	

	@Override
	public RiskInfoDto auditRisk(String bizType, String obejctId, String objectType, JSONObject eventData,String operator,Boolean isAudit) {
		Map<String,Object> ctx = PointRiskHelper.getContext(bizType,obejctId,objectType,eventData);
		return super.auditRisk(bizType, obejctId, objectType, ctx, operator,isAudit);
	}


}
