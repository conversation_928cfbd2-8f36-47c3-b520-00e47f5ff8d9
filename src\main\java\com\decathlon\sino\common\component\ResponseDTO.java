package com.decathlon.sino.common.component;

import lombok.Data;

@Data
public class ResponseDTO<T> {

    public static final String OK = "0";

    public static final String FAIL = "400";

    public static final String ERROR = "500";

    public static final String CODE_NODE = "code";

    public static final String DATA_NODE = "data";

    private String code;
    private String message = "OK";

    private T data;

    private ResponseDTO(String code) {
        this.code = code;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public ResponseDTO(T data) {
        this.code = OK;
        this.data = data;
    }

    public ResponseDTO() {
    }

    public ResponseDTO(String code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public ResponseDTO(String code, T data) {
        this.code = code;
        this.data = data;
    }

    public ResponseDTO(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static <D> ResponseDTO<D> success(D data) {
        return new ResponseDTO<>(OK, data);
    }

    public static ResponseDTO<Void> success() {
        return new ResponseDTO<>(OK);
    }

    public static ResponseDTO<Void> err(String msg) {
        return new ResponseDTO<>(ERROR, msg);
    }

    public static ResponseDTO<Void> fail(String msg) {
        return new ResponseDTO<>(FAIL, msg);
    }

    public static ResponseDTO<Void> fail(String code, String msg) {
        return new ResponseDTO<>(code, msg);
    }

}
