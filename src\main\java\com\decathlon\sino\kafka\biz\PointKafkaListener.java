package com.decathlon.sino.kafka.biz;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.stereotype.Component;

import com.decathlon.sino.kafka.biz.helper.PointListenerProcessHelper;

@Slf4j
@Component
@AllArgsConstructor
public class PointKafkaListener implements KafkaListenerStrategy {
	
	private final PointListenerProcessHelper pointListenerProcessHelper;
	
    @Override
    public void handleMessage(ConsumerRecord<String, Object> records) {
        try {
            log.info("Processing message: {}", records.value());
            pointListenerProcessHelper.process(records);
        } catch (Exception e) {
            log.error("Error processing message: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public String getBizName() {
        return "POINT";
    }

    @Override
    public String getDescription() {
        return "Message processing strategy with retry mechanism";
    }
} 