package com.decathlon.sino.data.entity;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.decathlon.sino.data.entity.basic.IdEntity;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@SuperBuilder
@Table(name = "rc_black_list_rule")
public class RcBlackListRuleEntity extends IdEntity {

	private static final long serialVersionUID = 1L;


	private String ruleCode;
	private String ruleName;
	private String bizType;
	private String objectType;
	private String groupCode;
	/**
	 * 表达式的触发时机
	 */
	private String triggerType;
	private String version;
	private String expression;
	private BigDecimal threshold;
	private BigDecimal weight;
	private Boolean status;
	private Date effectiveFrom;
	private Date effectiveTo;
	private Date createTime;
	private Date updateTime;
	private String createBy;
	private String updateBy;

}
