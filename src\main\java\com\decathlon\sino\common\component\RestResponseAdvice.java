package com.decathlon.sino.common.component;

import net.sf.json.JSONObject;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.validation.constraints.NotNull;

@RestControllerAdvice(basePackages = "com.decathlon.sino.controller")
public class RestResponseAdvice implements ResponseBodyAdvice<Object> {

    public boolean supports(@NotNull MethodParameter returnType, @NotNull Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    public Object beforeBodyWrite(Object body, @NotNull MethodParameter returnType, @NotNull MediaType selectedContentType,
                                  @NotNull Class<? extends HttpMessageConverter<?>> selectedConverterType, @NotNull ServerHttpRequest request,
                                  ServerHttpResponse response) {
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        ResponseDTO<Object> result = ResponseDTO.success(body);
        if (body == null) {
            if (returnType.getParameterType().isAssignableFrom(String.class)) {
                return JSONObject.fromObject(result).toString();
            }
        } else if (body instanceof ResponseDTO) {
            return body;
        } else if (body instanceof String) {
            return JSONObject.fromObject(result).toString();
        }
        return result;
    }

}
