package com.decathlon.sino.common.util;

import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.SpelParserConfiguration;

import java.util.List;

import org.springframework.expression.Expression;

public class Utils {
    public static String reverse(String input) {
        return new StringBuilder(input).reverse().toString();
    }
    
    //sum
    public static Integer sum(List<Integer> list) {
		Integer sum = 0;
		for (Integer i : list) {
			sum += i;
		}
		return sum;
	}
    
    public static void main(String[] args) throws NoSuchMethodException, SecurityException {
		
    	StandardEvaluationContext context = new StandardEvaluationContext();
    	context.registerFunction("reverse", Utils.class.getDeclaredMethod("reverse", String.class));
    	context.registerFunction("sum", Utils.class.getDeclaredMethod("sum", List.class));
    	

    	ExpressionParser parser = new SpelExpressionParser();
    	Expression exp = parser.parseExpression("#reverse('hello')");
    	String result = (String) exp.getValue(context);
    	System.out.println(result); // 输出: olleh
    	
    	List<Integer> list = List.of(1, 2, 3, 4, 5);
    	context.setVariable("list", list);
    	
    	Expression exp1 = parser.parseExpression("#sum(#list)");
    	Integer result1 = (Integer)exp1.getValue(context);
    	System.out.println(result1); // 输出: olleh
	}
}


